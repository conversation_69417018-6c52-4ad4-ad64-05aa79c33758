-- Entité Munitions Moyennes pour Apocalypse Survival
AddCSLuaFile("cl_init.lua")
AddCSLuaFile("shared.lua")
include("shared.lua")

function ENT:Initialize()
    self:SetModel("models/items/ammocrate_ar2.mdl")
    self:PhysicsInit(SOLID_VPHYSICS)
    self:SetMoveType(MOVETYPE_VPHYSICS)
    self:SetSolid(SOLID_VPHYSICS)
    self:SetUseType(SIMPLE_USE)
    
    local phys = self:GetPhysicsObject()
    if IsValid(phys) then
        phys:Wake()
        phys:SetMass(8)
    end
    
    -- Marquer comme objet de survie
    self:SetNWString("ApocalypseSurvival_ItemID", "rifle_ammo")
    self:SetNWInt("ApocalypseSurvival_Quantity", 60)
    
    -- Couleur pour identifier
    self:SetColor(Color(100, 255, 100, 255))
end

function ENT:Use(activator, caller)
    -- Cette fonction sera gérée par le système de ramassage
end

function ENT:OnTakeDamage(dmginfo)
    -- Résistant aux dégâts
    return false
end
