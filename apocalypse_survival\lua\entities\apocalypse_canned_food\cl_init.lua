-- Entité Conserve - Côté client
include("shared.lua")

function ENT:Draw()
    self:DrawModel()
    
    -- A<PERSON><PERSON><PERSON> le nom au-dessus de l'entité
    local pos = self:GetPos() + Vector(0, 0, 15)
    local ang = LocalPlayer():EyeAngles()
    ang:RotateAroundAxis(ang:Forward(), 90)
    ang:RotateAroundAxis(ang:Right(), 90)
    
    cam.Start3D2D(pos, ang, 0.1)
        draw.SimpleText("Conserve", "DermaDefaultBold", 0, 0, Color(100, 200, 100, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    cam.End3D2D()
end
