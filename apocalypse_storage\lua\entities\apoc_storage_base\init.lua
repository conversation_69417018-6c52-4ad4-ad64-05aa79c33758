-- Apocalypse Storage Base Entity - Server
AddCSLuaFile("cl_init.lua")
AddCSLuaFile("shared.lua")
include("shared.lua")

-- S'assurer que ApocalypseStorage existe
ApocalypseStorage = ApocalypseStorage or {}
ApocalypseStorage.Config = ApocalypseStorage.Config or {
    enableParticles = true,
    enableSounds = true,
    soundVolume = 0.5,
    maxStorageDistance = 100
}

-- Types de stockage par défaut
ApocalypseStorage.StorageTypes = ApocalypseStorage.StorageTypes or {
    ["backpack"] = {
        name = "Sac à dos",
        slots = 12,
        maxWeight = 50,
        model = "models/props_c17/suitcase_passenger_physics.mdl",
        portable = true,
        lockable = false,
        description = "Stockage portable limité pour les objets essentiels"
    },
    ["locker"] = {
        name = "Casier métallique",
        slots = 20,
        maxWeight = 100,
        model = "models/props_c17/lockers001a.mdl",
        portable = false,
        lockable = true,
        description = "Casier personnel sécurisé"
    },
    ["safe"] = {
        name = "Coffre-fort",
        slots = 30,
        maxWeight = 200,
        model = "models/props_c17/FurnitureDresser001a.mdl",
        portable = false,
        lockable = true,
        description = "Stockage sécurisé haute capacité"
    },
    ["crate"] = {
        name = "Caisse de survie",
        slots = 15,
        maxWeight = 75,
        model = "models/props_junk/wood_crate001a.mdl",
        portable = false,
        lockable = false,
        description = "Stockage temporaire pour matériel de survie"
    },
    ["cabinet"] = {
        name = "Armoire",
        slots = 25,
        maxWeight = 150,
        model = "models/props_c17/FurnitureWardrobeCloset001a.mdl",
        portable = false,
        lockable = true,
        description = "Grande armoire pour stockage général"
    }
}

-- Fonction pour obtenir les informations d'un type de stockage
ApocalypseStorage.GetStorageInfo = ApocalypseStorage.GetStorageInfo or function(storageType)
    return ApocalypseStorage.StorageTypes[storageType] or ApocalypseStorage.StorageTypes["backpack"]
end

-- Fonctions de sécurité pour éviter les erreurs
ApocalypseStorage.StorageData = ApocalypseStorage.StorageData or {}

ApocalypseStorage.GetStorageInventory = ApocalypseStorage.GetStorageInventory or function(storageID)
    if not ApocalypseStorage.StorageData[storageID] then
        ApocalypseStorage.StorageData[storageID] = {
            items = {},
            locked = false,
            owner = nil,
            lastAccess = os.time()
        }
    end
    return ApocalypseStorage.StorageData[storageID]
end

ApocalypseStorage.SaveStorageData = ApocalypseStorage.SaveStorageData or function()
    -- Fonction de sauvegarde de base
    local data = util.TableToJSON(ApocalypseStorage.StorageData)
    file.Write("apocalypse_storage_data.txt", data)
end

function ENT:Initialize()
    self:SetModel(self.StorageModel or "models/props_c17/suitcase_passenger_physics.mdl")
    self:PhysicsInit(SOLID_VPHYSICS)
    self:SetMoveType(MOVETYPE_VPHYSICS)
    self:SetSolid(SOLID_VPHYSICS)
    self:SetUseType(SIMPLE_USE)

    local phys = self:GetPhysicsObject()
    if IsValid(phys) then
        phys:Wake()
        phys:SetMass(50)
    end

    -- Générer un ID unique pour ce stockage
    self:SetStorageID(self:EntIndex() .. "_" .. os.time())
    self:SetStorageType(self.StorageType or "backpack")
    self:SetLocked(false)
    self:SetOwner(nil)

    -- Effets visuels
    if ApocalypseStorage.Config and ApocalypseStorage.Config.enableParticles then
        timer.Simple(0.1, function()
            if IsValid(self) then
                self:CreateParticleEffect()
            end
        end)
    end
end

-- S'assurer que les sons existent
ApocalypseStorage.Sounds = ApocalypseStorage.Sounds or {
    open = "doors/door_metal_thin_open1.wav",
    close = "doors/door_metal_thin_close2.wav",
    lock = "buttons/lever7.wav",
    unlock = "buttons/lever8.wav",
    error = "buttons/button10.wav",
    pickup = "items/ammo_pickup.wav"
}

function ENT:Use(activator, caller)
    if not IsValid(activator) or not activator:IsPlayer() then return end

    -- Vérification simple de distance si la fonction n'existe pas
    local canAccess = true
    if ApocalypseStorage.CanPlayerAccess then
        canAccess = ApocalypseStorage.CanPlayerAccess(activator, self)
    else
        local distance = activator:GetPos():Distance(self:GetPos())
        canAccess = distance <= (ApocalypseStorage.Config.maxStorageDistance or 100)
    end

    if not canAccess then
        activator:ChatPrint("[Stockage] Vous ne pouvez pas accéder à ce stockage")
        if ApocalypseStorage.Config and ApocalypseStorage.Config.enableSounds then
            self:EmitSound(ApocalypseStorage.Sounds.error, ApocalypseStorage.Config.soundVolume or 0.5)
        end
        return
    end

    -- Ouvrir le menu de stockage
    net.Start("ApocalypseStorage_OpenMenu")
    net.WriteEntity(self)
    net.Send(activator)
end

function ENT:CreateParticleEffect()
    -- Effet de particules subtil pour indiquer que c'est un stockage
    local effectdata = EffectData()
    effectdata:SetOrigin(self:GetPos() + Vector(0, 0, 20))
    effectdata:SetMagnitude(1)
    effectdata:SetScale(0.5)
    util.Effect("sparks", effectdata)
end

function ENT:OnTakeDamage(dmginfo)
    -- Les stockages peuvent être endommagés mais pas détruits facilement
    local damage = dmginfo:GetDamage()
    local health = self:Health() - damage
    
    if health <= 0 then
        -- Faire tomber tous les objets stockés
        self:DropAllItems()
        self:Remove()
    else
        self:SetHealth(health)
    end
end

function ENT:DropAllItems()
    local storageID = self:GetStorageID()
    if not storageID or storageID == "" then return end

    local inventory = ApocalypseStorage.GetStorageInventory(storageID)

    if inventory and inventory.items then
        for _, item in pairs(inventory.items) do
            -- Créer l'entité d'arme dans le monde
            local weapon = ents.Create(item.class)
            if IsValid(weapon) then
                weapon:SetPos(self:GetPos() + Vector(math.random(-50, 50), math.random(-50, 50), 20))
                weapon:SetAngles(AngleRand())
                weapon:Spawn()

                -- Ajouter un peu de vélocité
                local phys = weapon:GetPhysicsObject()
                if IsValid(phys) then
                    phys:SetVelocity(VectorRand() * 100)
                end
            end
        end
    end

    -- Vider l'inventaire
    if ApocalypseStorage.StorageData then
        ApocalypseStorage.StorageData[storageID] = nil
    end

    if ApocalypseStorage.SaveStorageData then
        ApocalypseStorage.SaveStorageData()
    end
end

function ENT:Think()
    -- Vérification périodique de l'état
    self:NextThink(CurTime() + 1)
    
    -- Effet visuel si verrouillé
    if self:GetLocked() then
        local effectdata = EffectData()
        effectdata:SetOrigin(self:GetPos() + Vector(0, 0, 30))
        effectdata:SetMagnitude(0.5)
        effectdata:SetScale(0.2)
        util.Effect("sparks", effectdata)
    end
    
    return true
end

-- Fonction pour obtenir les informations de stockage
function ENT:GetStorageInfo()
    return ApocalypseStorage.GetStorageInfo(self:GetStorageType())
end

-- Sauvegarde des données lors de la suppression
function ENT:OnRemove()
    if ApocalypseStorage.SaveStorageData then
        ApocalypseStorage.SaveStorageData()
    end
end
