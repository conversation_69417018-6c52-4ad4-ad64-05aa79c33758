-- Apocalypse Storage Base Entity - Client
include("shared.lua")

-- S'assurer que ApocalypseStorage existe côté client
ApocalypseStorage = ApocalypseStorage or {}
ApocalypseStorage.Config = ApocalypseStorage.Config or {
    enableParticles = true,
    enableSounds = true,
    soundVolume = 0.5,
    maxStorageDistance = 100
}

-- Types de stockage par défaut côté client
ApocalypseStorage.StorageTypes = ApocalypseStorage.StorageTypes or {
    ["backpack"] = {
        name = "Sac à dos",
        slots = 12,
        maxWeight = 50,
        description = "Stockage portable limité pour les objets essentiels"
    },
    ["locker"] = {
        name = "Casier métallique",
        slots = 20,
        maxWeight = 100,
        description = "Casier personnel sécurisé"
    },
    ["safe"] = {
        name = "Coffre-fort",
        slots = 30,
        maxWeight = 200,
        description = "Stockage sécurisé haute capacité"
    },
    ["crate"] = {
        name = "Caisse de survie",
        slots = 15,
        maxWeight = 75,
        description = "Stockage temporaire pour matériel de survie"
    },
    ["cabinet"] = {
        name = "Armoire",
        slots = 25,
        maxWeight = 150,
        description = "Grande armoire pour stockage général"
    }
}

ApocalypseStorage.GetStorageInfo = ApocalypseStorage.GetStorageInfo or function(storageType)
    return ApocalypseStorage.StorageTypes[storageType] or ApocalypseStorage.StorageTypes["backpack"]
end

function ENT:Initialize()
    self.NextParticleTime = 0
    self.GlowEffect = 0
end

function ENT:Draw()
    self:DrawModel()

    -- Effet de lueur pour les stockages
    if CurTime() > self.NextParticleTime then
        self.NextParticleTime = CurTime() + 2

        if ApocalypseStorage.Config and ApocalypseStorage.Config.enableParticles then
            local dlight = DynamicLight(self:EntIndex())
            if dlight then
                dlight.pos = self:GetPos() + Vector(0, 0, 20)
                dlight.r = 100
                dlight.g = 150
                dlight.b = 200
                dlight.brightness = 0.5
                dlight.decay = 1000
                dlight.size = 64
                dlight.dietime = CurTime() + 1
            end
        end
    end

    -- Affichage du texte d'information
    local ply = LocalPlayer()
    if IsValid(ply) then
        local distance = ply:GetPos():Distance(self:GetPos())

        if distance < 150 then
            self:DrawStorageInfo(ply, distance)
        end
    end
end

function ENT:DrawStorageInfo(ply, distance)
    local pos = self:GetPos() + Vector(0, 0, 40)
    local ang = (ply:GetPos() - pos):Angle()
    ang:RotateAroundAxis(ang:Forward(), 90)
    ang:RotateAroundAxis(ang:Right(), 90)
    
    cam.Start3D2D(pos, ang, 0.1)
        -- Arrière-plan
        draw.RoundedBox(8, -100, -60, 200, 120, Color(40, 40, 40, 200))
        
        -- Titre
        local storageInfo = self:GetStorageInfo()
        draw.SimpleText(storageInfo.name, "DermaLarge", 0, -40, Color(220, 220, 220), TEXT_ALIGN_CENTER)
        
        -- Informations
        local storageID = self:GetStorageID()
        local text = "Distance: " .. math.floor(distance) .. "m"
        draw.SimpleText(text, "DermaDefault", 0, -10, Color(180, 180, 180), TEXT_ALIGN_CENTER)
        
        -- État de verrouillage
        if self:GetLocked() then
            draw.SimpleText("🔒 VERROUILLÉ", "DermaDefault", 0, 10, Color(200, 100, 100), TEXT_ALIGN_CENTER)
        else
            draw.SimpleText("Appuyez sur E pour ouvrir", "DermaDefault", 0, 10, Color(100, 200, 100), TEXT_ALIGN_CENTER)
        end
        
        -- Capacité (si on a les données)
        if ApocalypseStorage.StorageData and ApocalypseStorage.StorageData[storageID] then
            local inventory = ApocalypseStorage.StorageData[storageID]
            local itemCount = #inventory.items
            local maxSlots = storageInfo.slots
            draw.SimpleText("Objets: " .. itemCount .. "/" .. maxSlots, "DermaDefault", 0, 30, Color(150, 150, 200), TEXT_ALIGN_CENTER)
        end
    cam.End3D2D()
end

function ENT:Think()
    -- Animation de lueur
    self.GlowEffect = math.sin(CurTime() * 2) * 0.3 + 0.7

    -- Particules pour stockage verrouillé
    if self:GetLocked() and ApocalypseStorage.Config and ApocalypseStorage.Config.enableParticles then
        if math.random(1, 100) < 5 then -- 5% de chance par frame
            local effectdata = EffectData()
            effectdata:SetOrigin(self:GetPos() + Vector(math.random(-10, 10), math.random(-10, 10), math.random(10, 30)))
            effectdata:SetMagnitude(0.3)
            effectdata:SetScale(0.1)
            util.Effect("sparks", effectdata)
        end
    end
end

-- Fonction pour obtenir les informations de stockage
function ENT:GetStorageInfo()
    return ApocalypseStorage.GetStorageInfo(self:GetStorageType())
end
