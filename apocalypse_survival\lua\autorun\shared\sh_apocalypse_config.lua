-- Configuration partagée pour le système de survie apocalypse
ApocalypseSurvival = ApocalypseSurvival or {}
ApocalypseSurvival.Config = {}

-- Configuration générale
ApocalypseSurvival.Config.MaxHunger = 100
ApocalypseSurvival.Config.MaxThirst = 100
ApocalypseSurvival.Config.MaxHealth = 100

-- Vitesse de dégradation (par seconde)
ApocalypseSurvival.Config.HungerDecayRate = 0.05  -- Perd 0.05 faim par seconde
ApocalypseSurvival.Config.ThirstDecayRate = 0.08  -- Perd 0.08 soif par seconde

-- Seuils critiques
ApocalypseSurvival.Config.CriticalHunger = 20
ApocalypseSurvival.Config.CriticalThirst = 15

-- Dégâts en cas de manque
ApocalypseSurvival.Config.StarvationDamage = 2    -- Dégâts par seconde si faim < 10
ApocalypseSurvival.Config.DehydrationDamage = 3   -- Dégâts par seconde si soif < 10

-- Configuration de l'inventaire unifié (style DayZ)
ApocalypseSurvival.Config.InventorySlots = {
    unified = 30       -- 30 slots pour tous les objets mélangés
}

-- Ancienne configuration (gardée pour compatibilité)
ApocalypseSurvival.Config.OldInventorySlots = {
    weapons = 2,        -- 2 slots pour les armes
    ammo = 8,          -- 8 slots pour les munitions
    food = 6,          -- 6 slots pour la nourriture
    medical = 4,       -- 4 slots pour les objets médicaux
    misc = 10          -- 10 slots pour divers objets
}

-- Touches par défaut (utiliser les constantes Garry's Mod)
ApocalypseSurvival.Config.InventoryKey = KEY_I or 73  -- Touche I
ApocalypseSurvival.Config.QuickUseKey = KEY_H or 72   -- Touche H

-- Couleurs de l'interface
ApocalypseSurvival.Config.Colors = {
    hunger_good = Color(0, 255, 0, 200),
    hunger_warning = Color(255, 255, 0, 200),
    hunger_critical = Color(255, 0, 0, 200),
    thirst_good = Color(0, 150, 255, 200),
    thirst_warning = Color(255, 255, 0, 200),
    thirst_critical = Color(255, 0, 0, 200),
    background = Color(0, 0, 0, 150),
    text = Color(255, 255, 255, 255)
}

-- Messages d'état
ApocalypseSurvival.Config.Messages = {
    hunger_critical = "Vous mourez de faim !",
    thirst_critical = "Vous mourez de soif !",
    inventory_full = "Inventaire plein !",
    item_consumed = "Objet consommé",
    item_added = "Objet ajouté à l'inventaire"
}

-- Effets visuels
ApocalypseSurvival.Config.Effects = {
    enable_screen_effects = true,
    enable_sound_effects = true,
    enable_chat_warnings = true
}

if SERVER then
    -- Intervalle de sauvegarde (en secondes)
    ApocalypseSurvival.Config.SaveInterval = 30
    
    -- Fichier de sauvegarde
    ApocalypseSurvival.Config.DataFile = "apocalypse_survival_data.txt"
end
