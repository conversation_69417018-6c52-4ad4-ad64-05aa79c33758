-- Apocalypse Storage System - Server
-- Gestion côté serveur du système de stockage

ApocalypseStorage = ApocalypseStorage or {}
ApocalypseStorage.StorageData = ApocalypseStorage.StorageData or {}

-- Charger les données de stockage
function ApocalypseStorage.LoadStorageData()
    local data = file.Read("apocalypse_storage_data.txt", "DATA")
    if data then
        ApocalypseStorage.StorageData = util.JSONToTable(data) or {}
        print("[Apocalypse Storage] Données de stockage chargées")
    else
        ApocalypseStorage.StorageData = {}
        print("[Apocalypse Storage] Nouvelles données de stockage créées")
    end
end

-- Sauvegarder les données de stockage
function ApocalypseStorage.SaveStorageData()
    local data = util.TableToJSON(ApocalypseStorage.StorageData)
    file.Write("apocalypse_storage_data.txt", data)
end

-- Obtenir l'inventaire d'un stockage
function ApocalypseStorage.GetStorageInventory(storageID)
    if not ApocalypseStorage.StorageData[storageID] then
        ApocalypseStorage.StorageData[storageID] = {
            items = {},
            locked = false,
            owner = nil,
            lastAccess = os.time()
        }
    end
    return ApocalypseStorage.StorageData[storageID]
end

-- Ajouter un objet au stockage
function ApocalypseStorage.AddItemToStorage(storageID, itemData)
    local inventory = ApocalypseStorage.GetStorageInventory(storageID)
    table.insert(inventory.items, itemData)
    inventory.lastAccess = os.time()
    ApocalypseStorage.SaveStorageData()
end

-- Retirer un objet du stockage
function ApocalypseStorage.RemoveItemFromStorage(storageID, itemIndex)
    local inventory = ApocalypseStorage.GetStorageInventory(storageID)
    if inventory.items[itemIndex] then
        local item = inventory.items[itemIndex]
        table.remove(inventory.items, itemIndex)
        inventory.lastAccess = os.time()
        ApocalypseStorage.SaveStorageData()
        return item
    end
    return nil
end

-- Calculer le poids total d'un stockage
function ApocalypseStorage.GetStorageWeight(storageID)
    local inventory = ApocalypseStorage.GetStorageInventory(storageID)
    local totalWeight = 0
    
    for _, item in pairs(inventory.items) do
        totalWeight = totalWeight + (item.weight or 1)
    end
    
    return totalWeight
end

-- Vérifier si on peut ajouter un objet
function ApocalypseStorage.CanAddItem(storageID, storageType, itemWeight)
    local inventory = ApocalypseStorage.GetStorageInventory(storageID)
    local storageInfo = ApocalypseStorage.GetStorageInfo(storageType)
    
    -- Vérifier le nombre de slots
    if #inventory.items >= storageInfo.slots then
        return false, "Stockage plein (slots)"
    end
    
    -- Vérifier le poids
    local currentWeight = ApocalypseStorage.GetStorageWeight(storageID)
    if currentWeight + itemWeight > storageInfo.maxWeight then
        return false, "Stockage plein (poids)"
    end
    
    return true
end

-- Réseau : Ouvrir le menu de stockage (seulement si pas déjà défini)
if not ApocalypseStorage.ServerNetworkHandlersLoaded then
    ApocalypseStorage.ServerNetworkHandlersLoaded = true

    net.Receive("ApocalypseStorage_OpenMenu", function(len, ply)
        local storage = net.ReadEntity()

        if not IsValid(storage) then return end

        -- Vérification simple de distance
        local distance = ply:GetPos():Distance(storage:GetPos())
        if distance > (ApocalypseStorage.Config.maxStorageDistance or 100) then
            return
        end

        local storageID = storage:GetStorageID()
        if not storageID or storageID == "" then return end

        local inventory = ApocalypseStorage.GetStorageInventory(storageID)

        net.Start("ApocalypseStorage_UpdateInventory")
        net.WriteString(storageID)
        net.WriteTable(inventory.items or {})
        net.WriteString(storage:GetStorageType() or "backpack")
        net.WriteBool(inventory.locked or false)
        net.Send(ply)

        -- Jouer le son d'ouverture
        if ApocalypseStorage.Config and ApocalypseStorage.Config.enableSounds and ApocalypseStorage.Sounds then
            storage:EmitSound(ApocalypseStorage.Sounds.open or "doors/door_metal_thin_open1.wav", ApocalypseStorage.Config.soundVolume or 0.5)
        end
    end)

-- Réseau : Stocker un objet
net.Receive("ApocalypseStorage_StoreItem", function(len, ply)
    local storage = net.ReadEntity()
    local weapon = ply:GetActiveWeapon()
    
    if not IsValid(storage) or not IsValid(weapon) or not ApocalypseStorage.CanPlayerAccess(ply, storage) then
        return
    end
    
    local storageID = storage:GetStorageID()
    local itemWeight = ApocalypseStorage.GetItemWeight(weapon)
    local canAdd, reason = ApocalypseStorage.CanAddItem(storageID, storage:GetStorageType(), itemWeight)
    
    if not canAdd then
        ply:ChatPrint("[Stockage] " .. reason)
        return
    end
    
    -- Créer les données de l'objet
    local itemData = {
        class = weapon:GetClass(),
        weight = itemWeight,
        ammo = weapon:GetPrimaryAmmoType() != -1 and ply:GetAmmoCount(weapon:GetPrimaryAmmoType()) or 0,
        timestamp = os.time()
    }
    
    -- Ajouter au stockage
    ApocalypseStorage.AddItemToStorage(storageID, itemData)
    
    -- Retirer l'arme du joueur
    ply:StripWeapon(weapon:GetClass())
    
    -- Mettre à jour l'inventaire
    local inventory = ApocalypseStorage.GetStorageInventory(storageID)
    net.Start("ApocalypseStorage_UpdateInventory")
    net.WriteString(storageID)
    net.WriteTable(inventory.items)
    net.WriteString(storage:GetStorageType())
    net.WriteBool(inventory.locked)
    net.Send(ply)
    
    ply:ChatPrint("[Stockage] Objet stocké: " .. weapon:GetClass())
    
    if ApocalypseStorage.Config.enableSounds then
        storage:EmitSound(ApocalypseStorage.Sounds.pickup, ApocalypseStorage.Config.soundVolume)
    end
end)

-- Réseau : Récupérer un objet
net.Receive("ApocalypseStorage_RetrieveItem", function(len, ply)
    local storage = net.ReadEntity()
    local itemIndex = net.ReadInt(16)
    
    if not IsValid(storage) or not ApocalypseStorage.CanPlayerAccess(ply, storage) then
        return
    end
    
    local storageID = storage:GetStorageID()
    local item = ApocalypseStorage.RemoveItemFromStorage(storageID, itemIndex)
    
    if item then
        -- Donner l'arme au joueur
        ply:Give(item.class)
        
        -- Restaurer les munitions si applicable
        if item.ammo and item.ammo > 0 then
            local weapon = ply:GetWeapon(item.class)
            if IsValid(weapon) then
                local ammoType = weapon:GetPrimaryAmmoType()
                if ammoType != -1 then
                    ply:SetAmmo(item.ammo, ammoType)
                end
            end
        end
        
        -- Mettre à jour l'inventaire
        local inventory = ApocalypseStorage.GetStorageInventory(storageID)
        net.Start("ApocalypseStorage_UpdateInventory")
        net.WriteString(storageID)
        net.WriteTable(inventory.items)
        net.WriteString(storage:GetStorageType())
        net.WriteBool(inventory.locked)
        net.Send(ply)
        
        ply:ChatPrint("[Stockage] Objet récupéré: " .. item.class)
        
        if ApocalypseStorage.Config.enableSounds then
            storage:EmitSound(ApocalypseStorage.Sounds.pickup, ApocalypseStorage.Config.soundVolume)
        end
    end
end)

-- Réseau : Basculer le verrouillage
net.Receive("ApocalypseStorage_ToggleLock", function(len, ply)
    local storage = net.ReadEntity()
    
    if not IsValid(storage) or not ApocalypseStorage.CanPlayerAccess(ply, storage) then
        return
    end
    
    local storageID = storage:GetStorageID()
    local inventory = ApocalypseStorage.GetStorageInventory(storageID)
    
    -- Seul le propriétaire peut verrouiller/déverrouiller
    if not inventory.owner then
        inventory.owner = ply:SteamID()
    elseif inventory.owner != ply:SteamID() then
        ply:ChatPrint("[Stockage] Vous n'êtes pas le propriétaire de ce stockage")
        return
    end
    
    inventory.locked = not inventory.locked
    storage:SetLocked(inventory.locked)
    ApocalypseStorage.SaveStorageData()
    
    local soundKey = inventory.locked and "lock" or "unlock"
    if ApocalypseStorage.Config.enableSounds then
        storage:EmitSound(ApocalypseStorage.Sounds[soundKey], ApocalypseStorage.Config.soundVolume)
    end
    
    ply:ChatPrint("[Stockage] " .. (inventory.locked and "Verrouillé" or "Déverrouillé"))
end)

end -- Fin du bloc conditionnel pour les gestionnaires réseau

-- Sauvegarde automatique
timer.Create("ApocalypseStorage_AutoSave", ApocalypseStorage.Config.saveInterval, 0, function()
    ApocalypseStorage.SaveStorageData()
end)

-- Charger les données au démarrage
hook.Add("Initialize", "ApocalypseStorage_LoadData", function()
    ApocalypseStorage.LoadStorageData()
end)

print("[Apocalypse Storage] Server-side loaded")
