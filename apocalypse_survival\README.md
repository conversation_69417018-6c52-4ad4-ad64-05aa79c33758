# Apocalypse Survival System

Un addon complet pour Garry's Mod RP apocalypse incluant des systèmes de soif, faim et inventaire limité.

## Fonctionnalités

### Système de Survie
- **Faim** : Diminue progressivement avec le temps (0.05 points/seconde)
- **Soif** : Diminue plus rapidement que la faim (0.08 points/seconde)
- **Dégâts** : Les joueurs subissent des dégâts quand faim < 10 ou soif < 10
- **Effets visuels** : Bordures rouges et pulsations quand les stats sont critiques

### Système d'Inventaire
- **Catégories** : Armes (2 slots), Munitions (8 slots), Nourriture (6 slots), Médical (4 slots), Divers (10 slots)
- **Interface graphique** : Ouvrir avec la touche [I]
- **Objets empilables** : Certains objets peuvent être empilés jusqu'à une limite
- **Utilisation** : Clic droit sur un objet consommable pour l'utiliser

### Objets Disponibles

#### Nourriture
- **Conserve** : +35 faim, +5 soif
- **Pain** : +20 faim
- **Pomme** : +15 faim, +10 soif
- **Viande cuite** : +40 faim, +5 vie

#### Boissons
- **Bouteille d'eau** : +50 soif
- **Eau sale** : +25 soif, -5 vie
- **Boisson énergisante** : +30 soif, +5 faim, +10 vie

#### Objets Médicaux
- **Bandage** : +25 vie
- **Trousse médicale** : +75 vie
- **Antidouleurs** : +15 vie

#### Munitions
- **Munitions pistolet** : Calibre 9mm
- **Munitions fusil** : Pour fusil d'assaut
- **Cartouches fusil à pompe** : Pour shotgun

## Installation

1. Placez le dossier `apocalypse_survival` dans votre répertoire `garrysmod/addons/`
2. Redémarrez votre serveur ou tapez `lua_run_cl` dans la console

## Commandes Admin

- `apocalypse_give_item <item_id> [quantité]` : Donner un objet
- `apocalypse_set_hunger <valeur>` : Définir la faim (0-100)
- `apocalypse_set_thirst <valeur>` : Définir la soif (0-100)

### Exemples d'objets :
- `canned_food` : Conserve
- `water_bottle` : Bouteille d'eau
- `bandage` : Bandage
- `pistol_ammo` : Munitions pistolet

## Configuration

Modifiez `lua/autorun/shared/sh_apocalypse_config.lua` pour :
- Ajuster les vitesses de dégradation
- Modifier les limites d'inventaire
- Changer les couleurs de l'interface
- Personnaliser les messages

## Persistance

Les données des joueurs (faim, soif, inventaire) sont automatiquement sauvegardées toutes les 30 secondes dans `data/apocalypse_survival_data.txt`.

## Interface

### HUD Principal
- Barres de faim, soif et vie en haut à gauche
- Indicateur d'inventaire en haut à droite
- Effets visuels pour les états critiques

### Inventaire
- Ouverture avec [I]
- Catégories organisées en grilles
- Tooltips informatifs
- Clic droit pour utiliser les objets

## Compatibilité

Compatible avec la plupart des addons Garry's Mod. Masque automatiquement les barres de vie par défaut pour éviter les conflits.

## Support

Pour des questions ou des problèmes, vérifiez la console pour les erreurs Lua et assurez-vous que tous les fichiers sont correctement placés.
