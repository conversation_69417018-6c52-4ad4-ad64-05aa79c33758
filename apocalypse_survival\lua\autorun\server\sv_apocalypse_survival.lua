-- Système de survie apocalypse - <PERSON><PERSON><PERSON> serveur
ApocalypseSurvival = ApocalypseSurvival or {}
ApocalypseSurvival.PlayerData = ApocalypseSurvival.PlayerData or {}

-- Initialisation des données joueur
function ApocalypseSurvival.InitPlayer(ply)
    local steamid = ply:SteamID()
    
    if not ApocalypseSurvival.PlayerData[steamid] then
        ApocalypseSurvival.PlayerData[steamid] = {
            hunger = ApocalypseSurvival.Config.MaxHunger,
            thirst = ApocalypseSurvival.Config.MaxThirst,
            inventory = {
                unified = {}  -- Inventaire unifié style DayZ
            },
            equipped_weapon = nil,  -- Arme actuellement équipée
            last_update = CurTime()
        }
    end
    
    -- Envoyer les données au client
    ApocalypseSurvival.SendPlayerData(ply)
end

-- Envoyer les données au client
function ApocalypseSurvival.SendPlayerData(ply)
    local steamid = ply:SteamID()
    local data = ApocalypseSurvival.PlayerData[steamid]
    
    if data then
        net.Start("ApocalypseSurvival_UpdateData")
        net.WriteFloat(data.hunger)
        net.WriteFloat(data.thirst)
        net.WriteTable(data.inventory)
        net.Send(ply)
    end
end

-- Mise à jour des stats de survie
function ApocalypseSurvival.UpdateSurvival()
    for _, ply in pairs(player.GetAll()) do
        if IsValid(ply) and ply:Alive() then
            local steamid = ply:SteamID()
            local data = ApocalypseSurvival.PlayerData[steamid]
            
            if data then
                local currentTime = CurTime()
                local deltaTime = currentTime - data.last_update
                
                -- Diminuer la faim et la soif
                data.hunger = math.max(0, data.hunger - (ApocalypseSurvival.Config.HungerDecayRate * deltaTime))
                data.thirst = math.max(0, data.thirst - (ApocalypseSurvival.Config.ThirstDecayRate * deltaTime))
                
                -- Appliquer les dégâts si nécessaire
                if data.hunger <= 10 then
                    ply:TakeDamage(ApocalypseSurvival.Config.StarvationDamage * deltaTime, ply, ply)
                    if ApocalypseSurvival.Config.Effects.enable_chat_warnings then
                        ply:ChatPrint(ApocalypseSurvival.Config.Messages.hunger_critical)
                    end
                end
                
                if data.thirst <= 10 then
                    ply:TakeDamage(ApocalypseSurvival.Config.DehydrationDamage * deltaTime, ply, ply)
                    if ApocalypseSurvival.Config.Effects.enable_chat_warnings then
                        ply:ChatPrint(ApocalypseSurvival.Config.Messages.thirst_critical)
                    end
                end
                
                data.last_update = currentTime
                
                -- Envoyer les données mises à jour
                ApocalypseSurvival.SendPlayerData(ply)
            end
        end
    end
end

-- Ajouter un objet à l'inventaire unifié
function ApocalypseSurvival.AddItem(ply, itemId, quantity)
    quantity = quantity or 1
    local steamid = ply:SteamID()
    local data = ApocalypseSurvival.PlayerData[steamid]
    local item = ApocalypseSurvival.GetItem(itemId)

    if not data or not item then return false end

    local inventory = data.inventory.unified
    local maxSlots = ApocalypseSurvival.Config.InventorySlots.unified

    -- Vérifier si l'objet peut être empilé
    if item.stackable then
        for i, slot in pairs(inventory) do
            if slot.id == itemId and slot.quantity < item.max_stack then
                local canAdd = math.min(quantity, item.max_stack - slot.quantity)
                slot.quantity = slot.quantity + canAdd
                quantity = quantity - canAdd

                if quantity <= 0 then
                    ApocalypseSurvival.SendPlayerData(ply)
                    return true
                end
            end
        end
    end

    -- Ajouter dans de nouveaux slots
    while quantity > 0 and #inventory < maxSlots do
        local addQuantity = item.stackable and math.min(quantity, item.max_stack) or 1

        table.insert(inventory, {
            id = itemId,
            quantity = addQuantity
        })

        quantity = quantity - addQuantity
    end

    ApocalypseSurvival.SendPlayerData(ply)
    return quantity <= 0
end

-- Utiliser un objet (inventaire unifié)
function ApocalypseSurvival.UseItem(ply, slotIndex)
    local steamid = ply:SteamID()
    local data = ApocalypseSurvival.PlayerData[steamid]

    if not data or not data.inventory.unified or not data.inventory.unified[slotIndex] then
        return false
    end

    local slot = data.inventory.unified[slotIndex]
    local item = ApocalypseSurvival.GetItem(slot.id)

    if not item then return false end

    -- Si c'est une arme, l'équiper
    if item.weapon_class then
        ApocalypseSurvival.EquipWeapon(ply, slot.id, slotIndex)
        return true
    end

    -- Si c'est un objet consommable
    if item.consumable then
        -- Appliquer les effets
        data.hunger = math.min(ApocalypseSurvival.Config.MaxHunger, data.hunger + item.hunger_restore)
        data.thirst = math.min(ApocalypseSurvival.Config.MaxThirst, data.thirst + item.thirst_restore)

        if item.health_restore ~= 0 then
            local newHealth = math.min(ApocalypseSurvival.Config.MaxHealth, ply:Health() + item.health_restore)
            ply:SetHealth(math.max(1, newHealth))
        end

        -- Diminuer la quantité
        slot.quantity = slot.quantity - 1

        if slot.quantity <= 0 then
            table.remove(data.inventory.unified, slotIndex)
        end

        ApocalypseSurvival.SendPlayerData(ply)
        ply:ChatPrint(ApocalypseSurvival.Config.Messages.item_consumed)
        return true
    end

    return false
end

-- Équiper une arme depuis l'inventaire
function ApocalypseSurvival.EquipWeapon(ply, itemId, slotIndex)
    local steamid = ply:SteamID()
    local data = ApocalypseSurvival.PlayerData[steamid]
    local item = ApocalypseSurvival.GetItem(itemId)

    if not data or not item or not item.weapon_class then return false end

    -- Si le joueur a déjà une arme équipée, la remettre dans l'inventaire
    if data.equipped_weapon then
        local currentWeapon = ply:GetActiveWeapon()
        if IsValid(currentWeapon) then
            local weaponClass = currentWeapon:GetClass()
            -- Trouver l'item correspondant à cette arme
            for id, weaponItem in pairs(ApocalypseSurvival.Items) do
                if weaponItem.weapon_class == weaponClass then
                    ApocalypseSurvival.AddItem(ply, id, 1)
                    break
                end
            end
            ply:StripWeapon(weaponClass)
        end
    end

    -- Équiper la nouvelle arme
    ply:Give(item.weapon_class)
    ply:SelectWeapon(item.weapon_class)
    data.equipped_weapon = itemId

    -- Retirer l'arme de l'inventaire
    table.remove(data.inventory.unified, slotIndex)

    ApocalypseSurvival.SendPlayerData(ply)
    ply:ChatPrint("Arme équipée : " .. item.name)
    return true
end

-- Événements
hook.Add("PlayerInitialSpawn", "ApocalypseSurvival_PlayerSpawn", function(ply)
    timer.Simple(1, function()
        if IsValid(ply) then
            ApocalypseSurvival.InitPlayer(ply)
        end
    end)
end)

-- Timer principal
timer.Create("ApocalypseSurvival_Update", 1, 0, function()
    ApocalypseSurvival.UpdateSurvival()
end)

-- Sauvegarde périodique
timer.Create("ApocalypseSurvival_Save", ApocalypseSurvival.Config.SaveInterval, 0, function()
    ApocalypseSurvival.SaveData()
end)

-- Sauvegarde des données
function ApocalypseSurvival.SaveData()
    local dataToSave = util.TableToJSON(ApocalypseSurvival.PlayerData)
    file.Write(ApocalypseSurvival.Config.DataFile, dataToSave)
end

-- Chargement des données
function ApocalypseSurvival.LoadData()
    if file.Exists(ApocalypseSurvival.Config.DataFile, "DATA") then
        local data = file.Read(ApocalypseSurvival.Config.DataFile, "DATA")
        if data then
            ApocalypseSurvival.PlayerData = util.JSONToTable(data) or {}
        end
    end
end

-- Charger les données au démarrage
ApocalypseSurvival.LoadData()

-- Networking
util.AddNetworkString("ApocalypseSurvival_UpdateData")
util.AddNetworkString("ApocalypseSurvival_UseItem")
util.AddNetworkString("ApocalypseSurvival_AddItem")

-- Recevoir les demandes d'utilisation d'objets (inventaire unifié)
net.Receive("ApocalypseSurvival_UseItem", function(len, ply)
    local slotIndex = net.ReadInt(8)
    ApocalypseSurvival.UseItem(ply, slotIndex)
end)

-- Désactiver le ramassage automatique des armes
hook.Add("PlayerCanPickupWeapon", "ApocalypseSurvival_BlockAutoPickup", function(ply, weapon)
    if not IsValid(ply) or not IsValid(weapon) then return end

    local weaponClass = weapon:GetClass()

    -- Permettre seulement certaines armes système
    local allowedWeapons = {
        "weapon_physgun",
        "gmod_tool",
        "weapon_physcannon"
    }

    for _, allowed in ipairs(allowedWeapons) do
        if weaponClass == allowed then
            return true -- Permettre le ramassage normal
        end
    end

    -- Bloquer le ramassage automatique pour toutes les autres armes
    return false
end)

-- Système de ramassage avec menu de choix
util.AddNetworkString("ApocalypseSurvival_ShowPickupMenu")
util.AddNetworkString("ApocalypseSurvival_PickupItem")

-- Fonction pour ramasser un objet/arme
function ApocalypseSurvival.PickupItem(ply, ent, action)
    if not IsValid(ply) or not IsValid(ent) then return false end

    -- Vérifier si c'est une arme
    if ent:IsWeapon() then
        local weaponClass = ent:GetClass()

        -- Trouver l'item correspondant
        local itemId = nil
        for id, item in pairs(ApocalypseSurvival.Items) do
            if item.weapon_class == weaponClass then
                itemId = id
                break
            end
        end

        if itemId then
            if action == "take" then
                if ApocalypseSurvival.AddItem(ply, itemId, 1) then
                    local item = ApocalypseSurvival.GetItem(itemId)
                    ply:ChatPrint("Arme ramassée : " .. item.name)
                    ent:Remove()
                    return true
                else
                    ply:ChatPrint("Inventaire plein !")
                    return false
                end
            elseif action == "equip" then
                -- Équiper directement l'arme
                ply:Give(weaponClass)
                ply:SelectWeapon(weaponClass)
                ent:Remove()
                ply:ChatPrint("Arme équipée : " .. weaponClass)
                return true
            end
        end
    end

    -- Vérifier si c'est un objet de survie
    local itemId = ent:GetNWString("ApocalypseSurvival_ItemID", "")
    if itemId ~= "" and ApocalypseSurvival.GetItem(itemId) then
        local quantity = ent:GetNWInt("ApocalypseSurvival_Quantity", 1)

        if action == "take" then
            if ApocalypseSurvival.AddItem(ply, itemId, quantity) then
                local item = ApocalypseSurvival.GetItem(itemId)
                ply:ChatPrint("Objet ramassé : " .. item.name .. " x" .. quantity)
                ent:Remove()
                return true
            else
                ply:ChatPrint("Inventaire plein !")
                return false
            end
        end
    end

    return false
end

-- Recevoir les demandes de ramassage
net.Receive("ApocalypseSurvival_PickupItem", function(len, ply)
    local entIndex = net.ReadInt(16)
    local action = net.ReadString()

    local ent = Entity(entIndex)
    ApocalypseSurvival.PickupItem(ply, ent, action)
end)

-- Commandes de test (admin seulement)
concommand.Add("apocalypse_give_item", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    local itemId = args[1]
    local quantity = tonumber(args[2]) or 1

    if itemId and ApocalypseSurvival.GetItem(itemId) then
        if ApocalypseSurvival.AddItem(ply, itemId, quantity) then
            ply:ChatPrint("Objet ajouté : " .. itemId .. " x" .. quantity)
        else
            ply:ChatPrint("Impossible d'ajouter l'objet (inventaire plein?)")
        end
    else
        ply:ChatPrint("Objet invalide : " .. (itemId or "nil"))
    end
end)

concommand.Add("apocalypse_set_hunger", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    local value = tonumber(args[1])
    if value then
        local steamid = ply:SteamID()
        if ApocalypseSurvival.PlayerData[steamid] then
            ApocalypseSurvival.PlayerData[steamid].hunger = math.Clamp(value, 0, 100)
            ApocalypseSurvival.SendPlayerData(ply)
            ply:ChatPrint("Faim définie à : " .. value)
        end
    end
end)

concommand.Add("apocalypse_set_thirst", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    local value = tonumber(args[1])
    if value then
        local steamid = ply:SteamID()
        if ApocalypseSurvival.PlayerData[steamid] then
            ApocalypseSurvival.PlayerData[steamid].thirst = math.Clamp(value, 0, 100)
            ApocalypseSurvival.SendPlayerData(ply)
            ply:ChatPrint("Soif définie à : " .. value)
        end
    end
end)

-- Commande pour créer des objets dans le monde
concommand.Add("apocalypse_spawn_item", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    local itemId = args[1]
    local quantity = tonumber(args[2]) or 1

    if not itemId or not ApocalypseSurvival.GetItem(itemId) then
        ply:ChatPrint("Objet invalide. Objets disponibles :")
        for id, item in pairs(ApocalypseSurvival.Items) do
            ply:ChatPrint("- " .. id .. " (" .. item.name .. ")")
        end
        return
    end

    -- Créer une entité prop_physics pour représenter l'objet
    local trace = ply:GetEyeTrace()
    local pos = trace.HitPos + trace.HitNormal * 10

    local item = ApocalypseSurvival.GetItem(itemId)
    local ent = ents.Create("prop_physics")

    -- Choisir le modèle selon le type d'objet
    local model = "models/props_junk/cardboard_box004a.mdl" -- Défaut
    if item.category == "food" then
        model = "models/props_junk/garbage_bag001a.mdl"
    elseif item.category == "medical" then
        model = "models/props_c17/briefcase001a.mdl"
    elseif item.category == "ammo" then
        model = "models/items/ammocrate_smg1.mdl"
    elseif item.category == "misc" then
        model = "models/props_c17/suitcase_passenger_physics.mdl"
    end

    ent:SetModel(model)
    ent:SetPos(pos)
    ent:Spawn()
    ent:Activate()

    -- Marquer l'entité comme objet de survie
    ent:SetNWString("ApocalypseSurvival_ItemID", itemId)
    ent:SetNWInt("ApocalypseSurvival_Quantity", quantity)

    ply:ChatPrint("Objet créé : " .. item.name .. " x" .. quantity)
end)

-- Commande pour donner une arme directement (pour test)
concommand.Add("apocalypse_give_weapon", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    local weaponClass = args[1]
    if not weaponClass then
        ply:ChatPrint("Usage: apocalypse_give_weapon <weapon_class>")
        return
    end

    -- Créer l'arme dans le monde pour qu'elle soit interceptée
    local trace = ply:GetEyeTrace()
    local pos = trace.HitPos + trace.HitNormal * 10

    local weapon = ents.Create(weaponClass)
    if IsValid(weapon) then
        weapon:SetPos(pos)
        weapon:Spawn()
        weapon:Activate()
        ply:ChatPrint("Arme créée : " .. weaponClass)
    else
        ply:ChatPrint("Arme invalide : " .. weaponClass)
    end
end)

-- Commande pour lister tous les objets disponibles
concommand.Add("apocalypse_list_items", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    ply:ChatPrint("=== OBJETS DISPONIBLES ===")

    local categories = {
        weapons = "🔫 ARMES",
        food = "🍖 NOURRITURE",
        medical = "🏥 MÉDICAL",
        ammo = "📦 MUNITIONS",
        misc = "🔧 DIVERS"
    }

    for category, title in pairs(categories) do
        ply:ChatPrint(title .. " :")
        for id, item in pairs(ApocalypseSurvival.Items) do
            if item.category == category then
                ply:ChatPrint("  - " .. id .. " (" .. item.name .. ")")
            end
        end
        ply:ChatPrint("")
    end

    ply:ChatPrint("Usage :")
    ply:ChatPrint("apocalypse_give_item <item_id> [quantité]")
    ply:ChatPrint("apocalypse_spawn_item <item_id> [quantité]")
end)

-- Commande d'aide complète
concommand.Add("apocalypse_admin_help", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    ply:ChatPrint("=== APOCALYPSE SURVIVAL - COMMANDES ADMIN ===")
    ply:ChatPrint("")
    ply:ChatPrint("GESTION DES OBJETS :")
    ply:ChatPrint("• apocalypse_give_item <item> [qty] - Donner objet à l'inventaire")
    ply:ChatPrint("• apocalypse_spawn_item <item> [qty] - Créer objet dans le monde")
    ply:ChatPrint("• apocalypse_give_weapon <class> - Créer arme dans le monde")
    ply:ChatPrint("• apocalypse_list_items - Lister tous les objets")
    ply:ChatPrint("")
    ply:ChatPrint("GESTION DES STATS :")
    ply:ChatPrint("• apocalypse_set_hunger <valeur> - Définir faim (0-100)")
    ply:ChatPrint("• apocalypse_set_thirst <valeur> - Définir soif (0-100)")
    ply:ChatPrint("")
    ply:ChatPrint("EXEMPLES :")
    ply:ChatPrint("apocalypse_give_item weapon_pistol 1")
    ply:ChatPrint("apocalypse_spawn_item canned_food 5")
    ply:ChatPrint("apocalypse_give_weapon weapon_shotgun")
    ply:ChatPrint("===========================================")
end)
