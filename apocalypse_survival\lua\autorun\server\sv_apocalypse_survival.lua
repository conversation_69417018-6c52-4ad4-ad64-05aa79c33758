-- Système de survie apocalypse - <PERSON><PERSON><PERSON> serveur
ApocalypseSurvival = ApocalypseSurvival or {}
ApocalypseSurvival.PlayerData = ApocalypseSurvival.PlayerData or {}

-- Initialisation des données joueur
function ApocalypseSurvival.InitPlayer(ply)
    local steamid = ply:SteamID()
    
    if not ApocalypseSurvival.PlayerData[steamid] then
        ApocalypseSurvival.PlayerData[steamid] = {
            hunger = ApocalypseSurvival.Config.MaxHunger,
            thirst = ApocalypseSurvival.Config.MaxThirst,
            inventory = {
                unified = {}  -- Inventaire unifié style DayZ
            },
            weapon_slots = {
                [1] = nil,  -- Slot d'arme primaire
                [2] = nil   -- Slot d'arme secondaire
            },
            last_update = CurTime()
        }
    else
        -- S'assurer que les weapon_slots existent pour les anciens joueurs
        if not ApocalypseSurvival.PlayerData[steamid].weapon_slots then
            ApocalypseSurvival.PlayerData[steamid].weapon_slots = {
                [1] = nil,
                [2] = nil
            }
        end
    end
    
    -- Envoyer les données au client
    ApocalypseSurvival.SendPlayerData(ply)
end

-- Envoyer les données au client
function ApocalypseSurvival.SendPlayerData(ply)
    local steamid = ply:SteamID()
    local data = ApocalypseSurvival.PlayerData[steamid]
    
    if data then
        net.Start("ApocalypseSurvival_UpdateData")
        net.WriteFloat(data.hunger)
        net.WriteFloat(data.thirst)
        net.WriteTable(data.inventory)
        net.Send(ply)
    end
end

-- Mise à jour des stats de survie
function ApocalypseSurvival.UpdateSurvival()
    for _, ply in pairs(player.GetAll()) do
        if IsValid(ply) and ply:Alive() then
            local steamid = ply:SteamID()
            local data = ApocalypseSurvival.PlayerData[steamid]
            
            if data then
                local currentTime = CurTime()
                local deltaTime = currentTime - data.last_update
                
                -- Diminuer la faim et la soif
                data.hunger = math.max(0, data.hunger - (ApocalypseSurvival.Config.HungerDecayRate * deltaTime))
                data.thirst = math.max(0, data.thirst - (ApocalypseSurvival.Config.ThirstDecayRate * deltaTime))
                
                -- Appliquer les dégâts si nécessaire
                if data.hunger <= 10 then
                    ply:TakeDamage(ApocalypseSurvival.Config.StarvationDamage * deltaTime, ply, ply)
                    if ApocalypseSurvival.Config.Effects.enable_chat_warnings then
                        ply:ChatPrint(ApocalypseSurvival.Config.Messages.hunger_critical)
                    end
                end
                
                if data.thirst <= 10 then
                    ply:TakeDamage(ApocalypseSurvival.Config.DehydrationDamage * deltaTime, ply, ply)
                    if ApocalypseSurvival.Config.Effects.enable_chat_warnings then
                        ply:ChatPrint(ApocalypseSurvival.Config.Messages.thirst_critical)
                    end
                end
                
                data.last_update = currentTime
                
                -- Envoyer les données mises à jour
                ApocalypseSurvival.SendPlayerData(ply)
            end
        end
    end
end

-- Ajouter un objet à l'inventaire unifié
function ApocalypseSurvival.AddItem(ply, itemId, quantity)
    quantity = quantity or 1
    local steamid = ply:SteamID()
    local data = ApocalypseSurvival.PlayerData[steamid]
    local item = ApocalypseSurvival.GetItem(itemId)

    print("[Apocalypse Survival] AddItem appelé:", ply:Nick(), itemId, quantity)

    if not data then
        print("[Apocalypse Survival] Pas de données joueur, initialisation...")
        ApocalypseSurvival.InitPlayer(ply)
        data = ApocalypseSurvival.PlayerData[steamid]
    end

    if not item then
        print("[Apocalypse Survival] Objet invalide:", itemId)
        return false
    end

    -- S'assurer que l'inventaire unifié existe
    if not data.inventory then
        data.inventory = {unified = {}}
    end
    if not data.inventory.unified then
        data.inventory.unified = {}
    end

    local inventory = data.inventory.unified
    local maxSlots = ApocalypseSurvival.Config.InventorySlots.unified

    print("[Apocalypse Survival] Inventaire actuel:", #inventory, "/", maxSlots)

    -- Vérifier si l'objet peut être empilé
    if item.stackable then
        for i, slot in pairs(inventory) do
            if slot.id == itemId and slot.quantity < item.max_stack then
                local canAdd = math.min(quantity, item.max_stack - slot.quantity)
                slot.quantity = slot.quantity + canAdd
                quantity = quantity - canAdd

                print("[Apocalypse Survival] Empilé dans slot existant:", canAdd)

                if quantity <= 0 then
                    ApocalypseSurvival.SendPlayerData(ply)
                    return true
                end
            end
        end
    end

    -- Ajouter dans de nouveaux slots
    while quantity > 0 and #inventory < maxSlots do
        local addQuantity = item.stackable and math.min(quantity, item.max_stack) or 1

        table.insert(inventory, {
            id = itemId,
            quantity = addQuantity
        })

        quantity = quantity - addQuantity
        print("[Apocalypse Survival] Nouveau slot créé:", addQuantity)
    end

    if quantity > 0 then
        print("[Apocalypse Survival] Inventaire plein, reste:", quantity)
    end

    ApocalypseSurvival.SendPlayerData(ply)
    return quantity <= 0
end

-- Utiliser un objet (inventaire unifié)
function ApocalypseSurvival.UseItem(ply, slotIndex)
    local steamid = ply:SteamID()
    local data = ApocalypseSurvival.PlayerData[steamid]

    if not data or not data.inventory.unified or not data.inventory.unified[slotIndex] then
        return false
    end

    local slot = data.inventory.unified[slotIndex]
    local item = ApocalypseSurvival.GetItem(slot.id)

    if not item then return false end

    -- Si c'est une arme, l'équiper
    if item.weapon_class then
        ApocalypseSurvival.EquipWeapon(ply, slot.id, slotIndex)
        return true
    end

    -- Si c'est un objet consommable
    if item.consumable then
        -- Appliquer les effets
        data.hunger = math.min(ApocalypseSurvival.Config.MaxHunger, data.hunger + item.hunger_restore)
        data.thirst = math.min(ApocalypseSurvival.Config.MaxThirst, data.thirst + item.thirst_restore)

        if item.health_restore ~= 0 then
            local newHealth = math.min(ApocalypseSurvival.Config.MaxHealth, ply:Health() + item.health_restore)
            ply:SetHealth(math.max(1, newHealth))
        end

        -- Diminuer la quantité
        slot.quantity = slot.quantity - 1

        if slot.quantity <= 0 then
            table.remove(data.inventory.unified, slotIndex)
        end

        ApocalypseSurvival.SendPlayerData(ply)
        ply:ChatPrint(ApocalypseSurvival.Config.Messages.item_consumed)
        return true
    end

    return false
end

-- Équiper une arme depuis l'inventaire dans un slot spécifique
function ApocalypseSurvival.EquipWeapon(ply, itemId, slotIndex)
    local steamid = ply:SteamID()
    local data = ApocalypseSurvival.PlayerData[steamid]
    local item = ApocalypseSurvival.GetItem(itemId)

    if not data or not item or not item.weapon_class then return false end

    -- Trouver un slot libre ou utiliser le slot 1 par défaut
    local weaponSlot = 1
    if data.weapon_slots[1] then
        if not data.weapon_slots[2] then
            weaponSlot = 2
        else
            -- Les deux slots sont occupés, remplacer le slot 1
            ApocalypseSurvival.UnequipWeapon(ply, 1)
            weaponSlot = 1
        end
    end

    -- Équiper la nouvelle arme
    ply:Give(item.weapon_class)
    ply:SelectWeapon(item.weapon_class)

    -- Enregistrer dans le slot
    data.weapon_slots[weaponSlot] = {
        itemId = itemId,
        weaponClass = item.weapon_class
    }

    -- Retirer l'arme de l'inventaire
    table.remove(data.inventory.unified, slotIndex)

    ApocalypseSurvival.SendPlayerData(ply)
    ply:ChatPrint("Arme équipée dans le slot " .. weaponSlot .. " : " .. item.name)
    return true
end

-- Déséquiper une arme et la remettre dans l'inventaire
function ApocalypseSurvival.UnequipWeapon(ply, weaponSlot)
    local steamid = ply:SteamID()
    local data = ApocalypseSurvival.PlayerData[steamid]

    if not data or not data.weapon_slots[weaponSlot] then return false end

    local weaponData = data.weapon_slots[weaponSlot]
    local weaponClass = weaponData.weaponClass
    local itemId = weaponData.itemId

    -- Retirer l'arme du joueur
    if ply:HasWeapon(weaponClass) then
        ply:StripWeapon(weaponClass)
    end

    -- Remettre dans l'inventaire
    if ApocalypseSurvival.AddItem(ply, itemId, 1) then
        data.weapon_slots[weaponSlot] = nil
        ApocalypseSurvival.SendPlayerData(ply)

        local item = ApocalypseSurvival.GetItem(itemId)
        ply:ChatPrint("Arme déséquipée : " .. (item and item.name or weaponClass))
        return true
    else
        ply:ChatPrint("Inventaire plein ! Impossible de déséquiper l'arme.")
        return false
    end
end

-- Système de gestion des munitions dans l'inventaire
ApocalypseSurvival.WeaponAmmoTypes = {
    ["weapon_pistol"] = "pistol_ammo",
    ["weapon_357"] = "pistol_ammo",
    ["weapon_smg1"] = "rifle_ammo",
    ["weapon_ar2"] = "rifle_ammo",
    ["weapon_shotgun"] = "shotgun_ammo",
    ["weapon_crossbow"] = "rifle_ammo"
}

-- Fonction pour obtenir les munitions d'un type depuis l'inventaire
function ApocalypseSurvival.GetAmmoFromInventory(ply, ammoType)
    local steamid = ply:SteamID()
    local data = ApocalypseSurvival.PlayerData[steamid]

    if not data or not data.inventory.unified then return 0 end

    local totalAmmo = 0
    for _, slot in pairs(data.inventory.unified) do
        local item = ApocalypseSurvival.GetItem(slot.id)
        if item and item.category == "ammo" and slot.id == ammoType then
            totalAmmo = totalAmmo + slot.quantity
        end
    end

    return totalAmmo
end

-- Fonction pour consommer des munitions de l'inventaire
function ApocalypseSurvival.ConsumeAmmoFromInventory(ply, ammoType, amount)
    local steamid = ply:SteamID()
    local data = ApocalypseSurvival.PlayerData[steamid]

    if not data or not data.inventory.unified then return false end

    local remaining = amount
    local inventory = data.inventory.unified

    -- Parcourir l'inventaire et consommer les munitions
    for i = #inventory, 1, -1 do
        local slot = inventory[i]
        local item = ApocalypseSurvival.GetItem(slot.id)

        if item and item.category == "ammo" and slot.id == ammoType then
            if slot.quantity <= remaining then
                remaining = remaining - slot.quantity
                table.remove(inventory, i)
            else
                slot.quantity = slot.quantity - remaining
                remaining = 0
            end

            if remaining <= 0 then break end
        end
    end

    ApocalypseSurvival.SendPlayerData(ply)
    return remaining == 0
end

-- Hook pour gérer les munitions des armes
hook.Add("Think", "ApocalypseSurvival_AmmoManagement", function()
    for _, ply in pairs(player.GetAll()) do
        if IsValid(ply) and ply:Alive() then
            local weapon = ply:GetActiveWeapon()
            if IsValid(weapon) then
                local weaponClass = weapon:GetClass()
                local ammoType = ApocalypseSurvival.WeaponAmmoTypes[weaponClass]

                if ammoType then
                    -- Vérifier les munitions dans l'inventaire
                    local inventoryAmmo = ApocalypseSurvival.GetAmmoFromInventory(ply, ammoType)
                    local weaponAmmo = weapon:Clip1()
                    local maxClip = weapon:GetMaxClip1()

                    -- Si l'arme n'a plus de munitions et qu'il y en a dans l'inventaire
                    if weaponAmmo <= 0 and inventoryAmmo > 0 then
                        local reloadAmount = math.min(maxClip, inventoryAmmo)
                        if ApocalypseSurvival.ConsumeAmmoFromInventory(ply, ammoType, reloadAmount) then
                            weapon:SetClip1(reloadAmount)
                            ply:ChatPrint("Rechargement automatique : " .. reloadAmount .. " munitions")
                        end
                    end
                end
            end
        end
    end
end)

-- Empêcher le rechargement normal et forcer l'utilisation de l'inventaire
hook.Add("PlayerSwitchWeapon", "ApocalypseSurvival_WeaponSwitch", function(ply, oldWeapon, newWeapon)
    if IsValid(newWeapon) then
        local weaponClass = newWeapon:GetClass()
        local ammoType = ApocalypseSurvival.WeaponAmmoTypes[weaponClass]

        if ammoType then
            -- Vider les munitions par défaut de l'arme
            timer.Simple(0.1, function()
                if IsValid(newWeapon) then
                    newWeapon:SetClip1(0)
                    ply:SetAmmo(0, newWeapon:GetPrimaryAmmoType())
                end
            end)
        end
    end
end)

-- Hook pour empêcher le ramassage de munitions par défaut
hook.Add("PlayerAmmoChanged", "ApocalypseSurvival_BlockDefaultAmmo", function(ply, ammoID, oldCount, newCount)
    -- Si les munitions augmentent (ramassage), les remettre à 0
    if newCount > oldCount then
        timer.Simple(0.1, function()
            if IsValid(ply) then
                ply:SetAmmo(0, ammoID)
            end
        end)
    end
end)

-- Rechargement manuel avec la touche R
hook.Add("PlayerButtonDown", "ApocalypseSurvival_ManualReload", function(ply, button)
    if button == KEY_R then
        local weapon = ply:GetActiveWeapon()
        if IsValid(weapon) then
            local weaponClass = weapon:GetClass()
            local ammoType = ApocalypseSurvival.WeaponAmmoTypes[weaponClass]

            if ammoType then
                local inventoryAmmo = ApocalypseSurvival.GetAmmoFromInventory(ply, ammoType)
                local weaponAmmo = weapon:Clip1()
                local maxClip = weapon:GetMaxClip1()

                if weaponAmmo < maxClip and inventoryAmmo > 0 then
                    local needed = maxClip - weaponAmmo
                    local reloadAmount = math.min(needed, inventoryAmmo)

                    if ApocalypseSurvival.ConsumeAmmoFromInventory(ply, ammoType, reloadAmount) then
                        weapon:SetClip1(weaponAmmo + reloadAmount)
                        ply:ChatPrint("Rechargé : +" .. reloadAmount .. " munitions (" .. (weaponAmmo + reloadAmount) .. "/" .. maxClip .. ")")
                    end
                else
                    if inventoryAmmo <= 0 then
                        ply:ChatPrint("Pas de munitions dans l'inventaire !")
                    else
                        ply:ChatPrint("Chargeur déjà plein !")
                    end
                end
            end
        end
    end
end)

-- Événements
hook.Add("PlayerInitialSpawn", "ApocalypseSurvival_PlayerSpawn", function(ply)
    timer.Simple(1, function()
        if IsValid(ply) then
            ApocalypseSurvival.InitPlayer(ply)
        end
    end)
end)

-- Timer principal
timer.Create("ApocalypseSurvival_Update", 1, 0, function()
    ApocalypseSurvival.UpdateSurvival()
end)

-- Sauvegarde périodique
timer.Create("ApocalypseSurvival_Save", ApocalypseSurvival.Config.SaveInterval, 0, function()
    ApocalypseSurvival.SaveData()
end)

-- Sauvegarde des données
function ApocalypseSurvival.SaveData()
    local dataToSave = util.TableToJSON(ApocalypseSurvival.PlayerData)
    file.Write(ApocalypseSurvival.Config.DataFile, dataToSave)
end

-- Chargement des données
function ApocalypseSurvival.LoadData()
    if file.Exists(ApocalypseSurvival.Config.DataFile, "DATA") then
        local data = file.Read(ApocalypseSurvival.Config.DataFile, "DATA")
        if data then
            ApocalypseSurvival.PlayerData = util.JSONToTable(data) or {}
        end
    end
end

-- Charger les données au démarrage
ApocalypseSurvival.LoadData()

-- Networking
util.AddNetworkString("ApocalypseSurvival_UpdateData")
util.AddNetworkString("ApocalypseSurvival_UseItem")
util.AddNetworkString("ApocalypseSurvival_AddItem")
util.AddNetworkString("ApocalypseSurvival_UnequipWeapon")

-- Recevoir les demandes d'utilisation d'objets (inventaire unifié)
net.Receive("ApocalypseSurvival_UseItem", function(len, ply)
    local slotIndex = net.ReadInt(8)
    ApocalypseSurvival.UseItem(ply, slotIndex)
end)

-- Recevoir les demandes de déséquipement d'armes
net.Receive("ApocalypseSurvival_UnequipWeapon", function(len, ply)
    local weaponSlot = net.ReadInt(8)
    ApocalypseSurvival.UnequipWeapon(ply, weaponSlot)
end)

-- Désactiver le ramassage automatique des armes ET munitions
hook.Add("PlayerCanPickupWeapon", "ApocalypseSurvival_BlockAutoPickup", function(ply, weapon)
    if not IsValid(ply) or not IsValid(weapon) then return end

    local weaponClass = weapon:GetClass()

    -- Permettre seulement certaines armes système
    local allowedWeapons = {
        "weapon_physgun",
        "gmod_tool",
        "weapon_physcannon"
    }

    for _, allowed in ipairs(allowedWeapons) do
        if weaponClass == allowed then
            return true -- Permettre le ramassage normal
        end
    end

    -- Bloquer le ramassage automatique pour toutes les autres armes
    return false
end)

-- Bloquer le ramassage automatique des munitions
hook.Add("PlayerCanPickupItem", "ApocalypseSurvival_BlockAmmoPickup", function(ply, item)
    if not IsValid(ply) or not IsValid(item) then return end

    -- Bloquer toutes les munitions par défaut de GMod
    local itemClass = item:GetClass()
    local blockedItems = {
        "item_ammo_pistol",
        "item_ammo_pistol_large",
        "item_ammo_smg1",
        "item_ammo_smg1_large",
        "item_ammo_ar2",
        "item_ammo_ar2_large",
        "item_ammo_357",
        "item_ammo_357_large",
        "item_ammo_crossbow",
        "item_box_buckshot",
        "item_rpg_round",
        "item_ammo_crate"
    }

    for _, blocked in ipairs(blockedItems) do
        if itemClass == blocked then
            return false -- Bloquer le ramassage
        end
    end

    return true -- Permettre les autres objets
end)

-- Système de ramassage avec menu de choix
util.AddNetworkString("ApocalypseSurvival_ShowPickupMenu")
util.AddNetworkString("ApocalypseSurvival_PickupItem")

-- Fonction pour ramasser un objet/arme
function ApocalypseSurvival.PickupItem(ply, ent, action)
    if not IsValid(ply) or not IsValid(ent) then return false end

    -- Vérifier si c'est une arme
    if ent:IsWeapon() then
        local weaponClass = ent:GetClass()

        -- Trouver l'item correspondant
        local itemId = nil
        for id, item in pairs(ApocalypseSurvival.Items) do
            if item.weapon_class == weaponClass then
                itemId = id
                break
            end
        end

        if itemId then
            if action == "take" then
                if ApocalypseSurvival.AddItem(ply, itemId, 1) then
                    local item = ApocalypseSurvival.GetItem(itemId)
                    ply:ChatPrint("Arme ramassée : " .. item.name)
                    ent:Remove()
                    return true
                else
                    ply:ChatPrint("Inventaire plein !")
                    return false
                end
            elseif action == "equip" then
                -- Équiper directement l'arme
                ply:Give(weaponClass)
                ply:SelectWeapon(weaponClass)
                ent:Remove()
                ply:ChatPrint("Arme équipée : " .. weaponClass)
                return true
            end
        end
    end

    -- Vérifier si c'est un objet de survie
    local itemId = ent:GetNWString("ApocalypseSurvival_ItemID", "")
    if itemId ~= "" and ApocalypseSurvival.GetItem(itemId) then
        local quantity = ent:GetNWInt("ApocalypseSurvival_Quantity", 1)

        if action == "take" then
            if ApocalypseSurvival.AddItem(ply, itemId, quantity) then
                local item = ApocalypseSurvival.GetItem(itemId)
                ply:ChatPrint("Objet ramassé : " .. item.name .. " x" .. quantity)
                ent:Remove()
                return true
            else
                ply:ChatPrint("Inventaire plein !")
                return false
            end
        end
    end

    return false
end

-- Recevoir les demandes de ramassage
net.Receive("ApocalypseSurvival_PickupItem", function(len, ply)
    local entIndex = net.ReadInt(16)
    local action = net.ReadString()

    print("[Apocalypse Survival] Demande de ramassage reçue:", ply:Nick(), entIndex, action)

    local ent = Entity(entIndex)
    if IsValid(ent) then
        print("[Apocalypse Survival] Entité valide:", ent:GetClass())
        local result = ApocalypseSurvival.PickupItem(ply, ent, action)
        print("[Apocalypse Survival] Résultat ramassage:", result)
    else
        print("[Apocalypse Survival] Entité invalide:", entIndex)
    end
end)

-- Commandes de test (admin seulement)
concommand.Add("apocalypse_give_item", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    local itemId = args[1]
    local quantity = tonumber(args[2]) or 1

    if itemId and ApocalypseSurvival.GetItem(itemId) then
        if ApocalypseSurvival.AddItem(ply, itemId, quantity) then
            ply:ChatPrint("Objet ajouté : " .. itemId .. " x" .. quantity)
        else
            ply:ChatPrint("Impossible d'ajouter l'objet (inventaire plein?)")
        end
    else
        ply:ChatPrint("Objet invalide : " .. (itemId or "nil"))
    end
end)

concommand.Add("apocalypse_set_hunger", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    local value = tonumber(args[1])
    if value then
        local steamid = ply:SteamID()
        if ApocalypseSurvival.PlayerData[steamid] then
            ApocalypseSurvival.PlayerData[steamid].hunger = math.Clamp(value, 0, 100)
            ApocalypseSurvival.SendPlayerData(ply)
            ply:ChatPrint("Faim définie à : " .. value)
        end
    end
end)

concommand.Add("apocalypse_set_thirst", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    local value = tonumber(args[1])
    if value then
        local steamid = ply:SteamID()
        if ApocalypseSurvival.PlayerData[steamid] then
            ApocalypseSurvival.PlayerData[steamid].thirst = math.Clamp(value, 0, 100)
            ApocalypseSurvival.SendPlayerData(ply)
            ply:ChatPrint("Soif définie à : " .. value)
        end
    end
end)

-- Commande pour créer des objets dans le monde
concommand.Add("apocalypse_spawn_item", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    local itemId = args[1]
    local quantity = tonumber(args[2]) or 1

    if not itemId or not ApocalypseSurvival.GetItem(itemId) then
        ply:ChatPrint("Objet invalide. Objets disponibles :")
        for id, item in pairs(ApocalypseSurvival.Items) do
            ply:ChatPrint("- " .. id .. " (" .. item.name .. ")")
        end
        return
    end

    -- Créer une entité prop_physics pour représenter l'objet
    local trace = ply:GetEyeTrace()
    local pos = trace.HitPos + trace.HitNormal * 10

    local item = ApocalypseSurvival.GetItem(itemId)
    local ent = ents.Create("prop_physics")

    -- Choisir le modèle selon le type d'objet
    local model = "models/props_junk/cardboard_box004a.mdl" -- Défaut
    if item.category == "food" then
        model = "models/props_junk/garbage_bag001a.mdl"
    elseif item.category == "medical" then
        model = "models/props_c17/briefcase001a.mdl"
    elseif item.category == "ammo" then
        model = "models/items/ammocrate_smg1.mdl"
    elseif item.category == "misc" then
        model = "models/props_c17/suitcase_passenger_physics.mdl"
    end

    ent:SetModel(model)
    ent:SetPos(pos)
    ent:Spawn()
    ent:Activate()

    -- Marquer l'entité comme objet de survie
    ent:SetNWString("ApocalypseSurvival_ItemID", itemId)
    ent:SetNWInt("ApocalypseSurvival_Quantity", quantity)

    ply:ChatPrint("Objet créé : " .. item.name .. " x" .. quantity)
end)

-- Commande pour donner une arme directement (pour test)
concommand.Add("apocalypse_give_weapon", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    local weaponClass = args[1]
    if not weaponClass then
        ply:ChatPrint("Usage: apocalypse_give_weapon <weapon_class>")
        return
    end

    -- Créer l'arme dans le monde pour qu'elle soit interceptée
    local trace = ply:GetEyeTrace()
    local pos = trace.HitPos + trace.HitNormal * 10

    local weapon = ents.Create(weaponClass)
    if IsValid(weapon) then
        weapon:SetPos(pos)
        weapon:Spawn()
        weapon:Activate()
        ply:ChatPrint("Arme créée : " .. weaponClass)
    else
        ply:ChatPrint("Arme invalide : " .. weaponClass)
    end
end)

-- Commande pour lister tous les objets disponibles
concommand.Add("apocalypse_list_items", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    ply:ChatPrint("=== OBJETS DISPONIBLES ===")

    local categories = {
        weapons = "🔫 ARMES",
        food = "🍖 NOURRITURE",
        medical = "🏥 MÉDICAL",
        ammo = "📦 MUNITIONS",
        misc = "🔧 DIVERS"
    }

    for category, title in pairs(categories) do
        ply:ChatPrint(title .. " :")
        for id, item in pairs(ApocalypseSurvival.Items) do
            if item.category == category then
                ply:ChatPrint("  - " .. id .. " (" .. item.name .. ")")
            end
        end
        ply:ChatPrint("")
    end

    ply:ChatPrint("Usage :")
    ply:ChatPrint("apocalypse_give_item <item_id> [quantité]")
    ply:ChatPrint("apocalypse_spawn_item <item_id> [quantité]")
end)

-- Commande d'aide complète
concommand.Add("apocalypse_admin_help", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    ply:ChatPrint("=== APOCALYPSE SURVIVAL - COMMANDES ADMIN ===")
    ply:ChatPrint("")
    ply:ChatPrint("GESTION DES OBJETS :")
    ply:ChatPrint("• apocalypse_give_item <item> [qty] - Donner objet à l'inventaire")
    ply:ChatPrint("• apocalypse_spawn_item <item> [qty] - Créer objet dans le monde")
    ply:ChatPrint("• apocalypse_give_weapon <class> - Créer arme dans le monde")
    ply:ChatPrint("• apocalypse_list_items - Lister tous les objets")
    ply:ChatPrint("")
    ply:ChatPrint("GESTION DES STATS :")
    ply:ChatPrint("• apocalypse_set_hunger <valeur> - Définir faim (0-100)")
    ply:ChatPrint("• apocalypse_set_thirst <valeur> - Définir soif (0-100)")
    ply:ChatPrint("")
    ply:ChatPrint("EXEMPLES :")
    ply:ChatPrint("apocalypse_give_item weapon_pistol 1")
    ply:ChatPrint("apocalypse_spawn_item canned_food 5")
    ply:ChatPrint("apocalypse_give_weapon weapon_shotgun")
    ply:ChatPrint("===========================================")
end)

-- Commandes pour spawner les entités spécifiques
concommand.Add("apocalypse_spawn_bandage", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    local trace = ply:GetEyeTrace()
    local pos = trace.HitPos + trace.HitNormal * 10

    local ent = ents.Create("apocalypse_bandage")
    ent:SetPos(pos)
    ent:Spawn()
    ent:Activate()

    ply:ChatPrint("Bandage créé")
end)

concommand.Add("apocalypse_spawn_ammo_light", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    local trace = ply:GetEyeTrace()
    local pos = trace.HitPos + trace.HitNormal * 10

    local ent = ents.Create("apocalypse_ammo_light")
    ent:SetPos(pos)
    ent:Spawn()
    ent:Activate()

    ply:ChatPrint("Munitions légères créées")
end)

concommand.Add("apocalypse_spawn_ammo_medium", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    local trace = ply:GetEyeTrace()
    local pos = trace.HitPos + trace.HitNormal * 10

    local ent = ents.Create("apocalypse_ammo_medium")
    ent:SetPos(pos)
    ent:Spawn()
    ent:Activate()

    ply:ChatPrint("Munitions moyennes créées")
end)

concommand.Add("apocalypse_spawn_ammo_heavy", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    local trace = ply:GetEyeTrace()
    local pos = trace.HitPos + trace.HitNormal * 10

    local ent = ents.Create("apocalypse_ammo_heavy")
    ent:SetPos(pos)
    ent:Spawn()
    ent:Activate()

    ply:ChatPrint("Munitions lourdes créées")
end)

-- Commande pour spawner un kit de survie complet
concommand.Add("apocalypse_spawn_survival_kit", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    local trace = ply:GetEyeTrace()
    local basePos = trace.HitPos + trace.HitNormal * 10

    -- Créer plusieurs objets autour du point
    local entities = {
        {class = "apocalypse_bandage", offset = Vector(0, 0, 0)},
        {class = "apocalypse_ammo_light", offset = Vector(50, 0, 0)},
        {class = "apocalypse_ammo_medium", offset = Vector(-50, 0, 0)},
        {class = "apocalypse_ammo_heavy", offset = Vector(0, 50, 0)},
    }

    for _, entData in ipairs(entities) do
        local ent = ents.Create(entData.class)
        ent:SetPos(basePos + entData.offset)
        ent:Spawn()
        ent:Activate()
    end

    -- Ajouter quelques objets via le système normal
    ApocalypseSurvival.AddItem(ply, "canned_food", 3)
    ApocalypseSurvival.AddItem(ply, "water_bottle", 2)

    ply:ChatPrint("Kit de survie créé !")
end)

-- Commande de test pour le networking
concommand.Add("apocalypse_test_pickup", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    print("[Apocalypse Survival] Test de ramassage pour:", ply:Nick())

    -- Créer un bandage de test
    local trace = ply:GetEyeTrace()
    local pos = trace.HitPos + trace.HitNormal * 10

    local ent = ents.Create("apocalypse_bandage")
    ent:SetPos(pos)
    ent:Spawn()
    ent:Activate()

    ply:ChatPrint("Bandage de test créé. Visez-le et appuyez sur E.")
    print("[Apocalypse Survival] Entité créée:", ent:EntIndex(), ent:GetClass())
end)

-- Commande pour spawner une conserve
concommand.Add("apocalypse_spawn_food", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    local trace = ply:GetEyeTrace()
    local pos = trace.HitPos + trace.HitNormal * 10

    local ent = ents.Create("apocalypse_canned_food")
    ent:SetPos(pos)
    ent:Spawn()
    ent:Activate()

    ply:ChatPrint("Conserve créée")
end)

-- Test simple d'ajout direct à l'inventaire
concommand.Add("apocalypse_test_add", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    print("[Apocalypse Survival] Test d'ajout direct pour:", ply:Nick())

    local result = ApocalypseSurvival.AddItem(ply, "bandage", 1)
    print("[Apocalypse Survival] Résultat ajout direct:", result)

    if result then
        ply:ChatPrint("Bandage ajouté directement à l'inventaire !")
    else
        ply:ChatPrint("Échec de l'ajout à l'inventaire")
    end
end)

-- Test du système de munitions
concommand.Add("apocalypse_test_ammo", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    print("[Apocalypse Survival] Test du système de munitions pour:", ply:Nick())

    -- Donner un pistolet et des munitions
    ply:Give("weapon_pistol")
    ply:SelectWeapon("weapon_pistol")

    -- Ajouter des munitions à l'inventaire
    ApocalypseSurvival.AddItem(ply, "pistol_ammo", 60)

    ply:ChatPrint("Pistolet donné avec 60 munitions dans l'inventaire")
    ply:ChatPrint("Appuyez sur [R] pour recharger depuis l'inventaire")
end)

-- Kit de combat complet
concommand.Add("apocalypse_combat_kit", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    -- Armes
    ply:Give("weapon_pistol")
    ply:Give("weapon_shotgun")
    ply:SelectWeapon("weapon_pistol")

    -- Munitions dans l'inventaire
    ApocalypseSurvival.AddItem(ply, "pistol_ammo", 120)
    ApocalypseSurvival.AddItem(ply, "shotgun_ammo", 40)

    -- Soins
    ApocalypseSurvival.AddItem(ply, "bandage", 5)
    ApocalypseSurvival.AddItem(ply, "medkit", 2)

    -- Nourriture
    ApocalypseSurvival.AddItem(ply, "canned_food", 3)
    ApocalypseSurvival.AddItem(ply, "water_bottle", 2)

    ply:ChatPrint("Kit de combat donné !")
    ply:ChatPrint("Armes: Pistolet + Shotgun")
    ply:ChatPrint("Munitions: 120 pistolet + 40 shotgun")
    ply:ChatPrint("Soins: 5 bandages + 2 trousses")
    ply:ChatPrint("Nourriture: 3 conserves + 2 bouteilles")
end)

-- Commande pour vider toutes les munitions par défaut
concommand.Add("apocalypse_clear_ammo", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    -- Vider toutes les munitions par défaut
    for i = 0, 20 do
        ply:SetAmmo(0, i)
    end

    -- Vider le chargeur de l'arme actuelle
    local weapon = ply:GetActiveWeapon()
    if IsValid(weapon) then
        weapon:SetClip1(0)
    end

    ply:ChatPrint("Toutes les munitions par défaut vidées")
end)

-- Test des slots d'armes
concommand.Add("apocalypse_test_slots", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    -- Donner plusieurs armes dans l'inventaire
    ApocalypseSurvival.AddItem(ply, "weapon_pistol", 1)
    ApocalypseSurvival.AddItem(ply, "weapon_shotgun", 1)
    ApocalypseSurvival.AddItem(ply, "weapon_smg", 1)

    -- Munitions
    ApocalypseSurvival.AddItem(ply, "pistol_ammo", 60)
    ApocalypseSurvival.AddItem(ply, "shotgun_ammo", 30)
    ApocalypseSurvival.AddItem(ply, "rifle_ammo", 90)

    ply:ChatPrint("Armes ajoutées à l'inventaire !")
    ply:ChatPrint("Ouvrez l'inventaire [I] et clic droit sur une arme pour l'équiper")
    ply:ChatPrint("Utilisez les slots d'armes en bas de l'inventaire")
end)
