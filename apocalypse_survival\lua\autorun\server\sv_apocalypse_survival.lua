-- Système de survie apocalypse - <PERSON><PERSON><PERSON> serveur
ApocalypseSurvival = ApocalypseSurvival or {}
ApocalypseSurvival.PlayerData = ApocalypseSurvival.PlayerData or {}

-- Initialisation des données joueur
function ApocalypseSurvival.InitPlayer(ply)
    local steamid = ply:SteamID()
    
    if not ApocalypseSurvival.PlayerData[steamid] then
        ApocalypseSurvival.PlayerData[steamid] = {
            hunger = ApocalypseSurvival.Config.MaxHunger,
            thirst = ApocalypseSurvival.Config.MaxThirst,
            inventory = {
                weapons = {},
                ammo = {},
                food = {},
                medical = {},
                misc = {}
            },
            last_update = CurTime()
        }
    end
    
    -- Envoyer les données au client
    ApocalypseSurvival.SendPlayerData(ply)
end

-- Envoyer les données au client
function ApocalypseSurvival.SendPlayerData(ply)
    local steamid = ply:SteamID()
    local data = ApocalypseSurvival.PlayerData[steamid]
    
    if data then
        net.Start("ApocalypseSurvival_UpdateData")
        net.WriteFloat(data.hunger)
        net.WriteFloat(data.thirst)
        net.WriteTable(data.inventory)
        net.Send(ply)
    end
end

-- Mise à jour des stats de survie
function ApocalypseSurvival.UpdateSurvival()
    for _, ply in pairs(player.GetAll()) do
        if IsValid(ply) and ply:Alive() then
            local steamid = ply:SteamID()
            local data = ApocalypseSurvival.PlayerData[steamid]
            
            if data then
                local currentTime = CurTime()
                local deltaTime = currentTime - data.last_update
                
                -- Diminuer la faim et la soif
                data.hunger = math.max(0, data.hunger - (ApocalypseSurvival.Config.HungerDecayRate * deltaTime))
                data.thirst = math.max(0, data.thirst - (ApocalypseSurvival.Config.ThirstDecayRate * deltaTime))
                
                -- Appliquer les dégâts si nécessaire
                if data.hunger <= 10 then
                    ply:TakeDamage(ApocalypseSurvival.Config.StarvationDamage * deltaTime, ply, ply)
                    if ApocalypseSurvival.Config.Effects.enable_chat_warnings then
                        ply:ChatPrint(ApocalypseSurvival.Config.Messages.hunger_critical)
                    end
                end
                
                if data.thirst <= 10 then
                    ply:TakeDamage(ApocalypseSurvival.Config.DehydrationDamage * deltaTime, ply, ply)
                    if ApocalypseSurvival.Config.Effects.enable_chat_warnings then
                        ply:ChatPrint(ApocalypseSurvival.Config.Messages.thirst_critical)
                    end
                end
                
                data.last_update = currentTime
                
                -- Envoyer les données mises à jour
                ApocalypseSurvival.SendPlayerData(ply)
            end
        end
    end
end

-- Ajouter un objet à l'inventaire
function ApocalypseSurvival.AddItem(ply, itemId, quantity)
    quantity = quantity or 1
    local steamid = ply:SteamID()
    local data = ApocalypseSurvival.PlayerData[steamid]
    local item = ApocalypseSurvival.GetItem(itemId)
    
    if not data or not item then return false end
    
    local category = item.category
    local inventory = data.inventory[category]
    local maxSlots = ApocalypseSurvival.Config.InventorySlots[category]
    
    if not inventory then return false end
    
    -- Vérifier si l'objet peut être empilé
    if item.stackable then
        for i, slot in pairs(inventory) do
            if slot.id == itemId and slot.quantity < item.max_stack then
                local canAdd = math.min(quantity, item.max_stack - slot.quantity)
                slot.quantity = slot.quantity + canAdd
                quantity = quantity - canAdd
                
                if quantity <= 0 then
                    ApocalypseSurvival.SendPlayerData(ply)
                    return true
                end
            end
        end
    end
    
    -- Ajouter dans de nouveaux slots
    while quantity > 0 and #inventory < maxSlots do
        local addQuantity = item.stackable and math.min(quantity, item.max_stack) or 1
        
        table.insert(inventory, {
            id = itemId,
            quantity = addQuantity
        })
        
        quantity = quantity - addQuantity
    end
    
    ApocalypseSurvival.SendPlayerData(ply)
    return quantity <= 0
end

-- Utiliser un objet
function ApocalypseSurvival.UseItem(ply, category, slotIndex)
    local steamid = ply:SteamID()
    local data = ApocalypseSurvival.PlayerData[steamid]
    
    if not data or not data.inventory[category] or not data.inventory[category][slotIndex] then
        return false
    end
    
    local slot = data.inventory[category][slotIndex]
    local item = ApocalypseSurvival.GetItem(slot.id)
    
    if not item or not item.consumable then return false end
    
    -- Appliquer les effets
    data.hunger = math.min(ApocalypseSurvival.Config.MaxHunger, data.hunger + item.hunger_restore)
    data.thirst = math.min(ApocalypseSurvival.Config.MaxThirst, data.thirst + item.thirst_restore)
    
    if item.health_restore ~= 0 then
        local newHealth = math.min(ApocalypseSurvival.Config.MaxHealth, ply:Health() + item.health_restore)
        ply:SetHealth(math.max(1, newHealth))
    end
    
    -- Diminuer la quantité
    slot.quantity = slot.quantity - 1
    
    if slot.quantity <= 0 then
        table.remove(data.inventory[category], slotIndex)
    end
    
    ApocalypseSurvival.SendPlayerData(ply)
    ply:ChatPrint(ApocalypseSurvival.Config.Messages.item_consumed)
    return true
end

-- Événements
hook.Add("PlayerInitialSpawn", "ApocalypseSurvival_PlayerSpawn", function(ply)
    timer.Simple(1, function()
        if IsValid(ply) then
            ApocalypseSurvival.InitPlayer(ply)
        end
    end)
end)

-- Timer principal
timer.Create("ApocalypseSurvival_Update", 1, 0, function()
    ApocalypseSurvival.UpdateSurvival()
end)

-- Sauvegarde périodique
timer.Create("ApocalypseSurvival_Save", ApocalypseSurvival.Config.SaveInterval, 0, function()
    ApocalypseSurvival.SaveData()
end)

-- Sauvegarde des données
function ApocalypseSurvival.SaveData()
    local dataToSave = util.TableToJSON(ApocalypseSurvival.PlayerData)
    file.Write(ApocalypseSurvival.Config.DataFile, dataToSave)
end

-- Chargement des données
function ApocalypseSurvival.LoadData()
    if file.Exists(ApocalypseSurvival.Config.DataFile, "DATA") then
        local data = file.Read(ApocalypseSurvival.Config.DataFile, "DATA")
        if data then
            ApocalypseSurvival.PlayerData = util.JSONToTable(data) or {}
        end
    end
end

-- Charger les données au démarrage
ApocalypseSurvival.LoadData()

-- Networking
util.AddNetworkString("ApocalypseSurvival_UpdateData")
util.AddNetworkString("ApocalypseSurvival_UseItem")
util.AddNetworkString("ApocalypseSurvival_AddItem")

-- Recevoir les demandes d'utilisation d'objets
net.Receive("ApocalypseSurvival_UseItem", function(len, ply)
    local category = net.ReadString()
    local slotIndex = net.ReadInt(8)
    ApocalypseSurvival.UseItem(ply, category, slotIndex)
end)

-- Commandes de test (admin seulement)
concommand.Add("apocalypse_give_item", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    local itemId = args[1]
    local quantity = tonumber(args[2]) or 1

    if itemId and ApocalypseSurvival.GetItem(itemId) then
        if ApocalypseSurvival.AddItem(ply, itemId, quantity) then
            ply:ChatPrint("Objet ajouté : " .. itemId .. " x" .. quantity)
        else
            ply:ChatPrint("Impossible d'ajouter l'objet (inventaire plein?)")
        end
    else
        ply:ChatPrint("Objet invalide : " .. (itemId or "nil"))
    end
end)

concommand.Add("apocalypse_set_hunger", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    local value = tonumber(args[1])
    if value then
        local steamid = ply:SteamID()
        if ApocalypseSurvival.PlayerData[steamid] then
            ApocalypseSurvival.PlayerData[steamid].hunger = math.Clamp(value, 0, 100)
            ApocalypseSurvival.SendPlayerData(ply)
            ply:ChatPrint("Faim définie à : " .. value)
        end
    end
end)

concommand.Add("apocalypse_set_thirst", function(ply, cmd, args)
    if not ply:IsAdmin() then return end

    local value = tonumber(args[1])
    if value then
        local steamid = ply:SteamID()
        if ApocalypseSurvival.PlayerData[steamid] then
            ApocalypseSurvival.PlayerData[steamid].thirst = math.Clamp(value, 0, 100)
            ApocalypseSurvival.SendPlayerData(ply)
            ply:ChatPrint("Soif définie à : " .. value)
        end
    end
end)
