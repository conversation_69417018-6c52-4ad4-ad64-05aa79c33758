-- Initialisation de l'addon Apocalypse Survival
-- <PERSON>chier s'assure que tous les composants sont chargés dans le bon ordre

ApocalypseSurvival = ApocalypseSurvival or {}

-- Charger la configuration en premier
if not ApocalypseSurvival.Config then
    include("autorun/shared/sh_apocalypse_config.lua")
    if SERVER then
        AddCSLuaFile("autorun/shared/sh_apocalypse_config.lua")
    end
end

-- Charger les définitions d'objets
if not ApocalypseSurvival.Items then
    include("autorun/shared/sh_apocalypse_items.lua")
    if SERVER then
        AddCSLuaFile("autorun/shared/sh_apocalypse_items.lua")
    end
end

-- C<PERSON><PERSON> serveur
if SERVER then
    -- Ajouter les fichiers client à la liste de téléchargement
    AddCSLuaFile("autorun/client/cl_apocalypse_hud.lua")
    AddCSLuaFile("autorun/client/cl_apocalypse_inventory.lua")
    
    -- Charger le script serveur
    include("autorun/server/sv_apocalypse_survival.lua")
    
    print("[Apocalypse Survival] Addon chargé côté serveur")
end

-- <PERSON>ôté client
if CLIENT then
    -- Charger les scripts client
    include("autorun/client/cl_apocalypse_hud.lua")
    include("autorun/client/cl_apocalypse_inventory.lua")
    
    print("[Apocalypse Survival] Addon chargé côté client")
end

-- Message de confirmation
print("[Apocalypse Survival] Système de survie apocalypse initialisé")
