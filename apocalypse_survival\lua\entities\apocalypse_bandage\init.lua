-- Entité Bandage pour Apocalypse Survival
AddCSLuaFile("cl_init.lua")
AddCSLuaFile("shared.lua")
include("shared.lua")

function ENT:Initialize()
    self:SetModel("models/props_c17/briefcase001a.mdl")
    self:PhysicsInit(SOLID_VPHYSICS)
    self:SetMoveType(MOVETYPE_VPHYSICS)
    self:SetSolid(SOLID_VPHYSICS)
    self:SetUseType(SIMPLE_USE)
    
    local phys = self:GetPhysicsObject()
    if IsValid(phys) then
        phys:Wake()
        phys:SetMass(5)
    end
    
    -- Marquer comme objet de survie
    self:SetNWString("ApocalypseSurvival_ItemID", "bandage")
    self:SetNWInt("ApocalypseSurvival_Quantity", 1)
    
    -- Couleur pour identifier
    self:SetColor(Color(255, 100, 100, 255))
end

function ENT:Use(activator, caller)
    -- Cette fonction sera gérée par le système de ramassage
end

function ENT:OnTakeDamage(dmginfo)
    -- Résistant aux dégâts
    return false
end
