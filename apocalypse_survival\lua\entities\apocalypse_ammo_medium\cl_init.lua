-- Entité Munitions Moyennes - Côté client
include("shared.lua")

function ENT:Draw()
    self:DrawModel()
    
    -- Aff<PERSON><PERSON> le nom au-dessus de l'entité
    local pos = self:GetPos() + Vector(0, 0, 20)
    local ang = LocalPlayer():EyeAngles()
    ang:RotateAroundAxis(ang:Forward(), 90)
    ang:RotateAroundAxis(ang:Right(), 90)
    
    cam.Start3D2D(pos, ang, 0.1)
        draw.SimpleText("Munitions Moyennes x60", "DermaDefaultBold", 0, 0, Color(100, 255, 100, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    cam.End3D2D()
end
