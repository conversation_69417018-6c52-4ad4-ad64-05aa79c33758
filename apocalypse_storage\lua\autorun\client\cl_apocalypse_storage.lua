-- Apocalypse Storage System - Client
-- Interface utilisateur et gestion côté client

ApocalypseStorage = ApocalypseStorage or {}
ApocalypseStorage.CurrentStorage = nil
ApocalypseStorage.StorageMenu = nil

-- Couleurs du thème apocalyptique
local colors = {
    background = Color(40, 40, 40, 240),
    header = Color(80, 60, 40, 255),
    button = Color(60, 60, 60, 255),
    buttonHover = Color(80, 80, 80, 255),
    text = Color(220, 220, 220, 255),
    textDark = Color(180, 180, 180, 255),
    accent = Color(200, 100, 50, 255),
    danger = Color(180, 50, 50, 255),
    success = Color(50, 150, 50, 255)
}

-- Créer l'interface de stockage
function ApocalypseStorage.CreateStorageMenu(storageID, items, storageType, isLocked)
    print("[Apocalypse Storage] Création du menu pour:", storageID, storageType)

    if IsValid(ApocalypseStorage.StorageMenu) then
        ApocalypseStorage.StorageMenu:Remove()
    end

    -- S'assurer que ApocalypseStorage.GetStorageInfo existe
    if not ApocalypseStorage.GetStorageInfo then
        print("[Apocalypse Storage] ERREUR: GetStorageInfo n'existe pas!")
        return
    end

    local storageInfo = ApocalypseStorage.GetStorageInfo(storageType)
    if not storageInfo then
        print("[Apocalypse Storage] ERREUR: Impossible d'obtenir les infos pour:", storageType)
        return
    end

    print("[Apocalypse Storage] Création de l'interface pour:", storageInfo.name)

    local frame = vgui.Create("DFrame")
    frame:SetSize(800, 600)
    frame:Center()
    frame:SetTitle("")
    frame:SetDraggable(true)
    frame:ShowCloseButton(false)
    frame:MakePopup()
    
    ApocalypseStorage.StorageMenu = frame
    
    -- Arrière-plan personnalisé
    frame.Paint = function(self, w, h)
        draw.RoundedBox(8, 0, 0, w, h, colors.background)
        draw.RoundedBox(8, 0, 0, w, 40, colors.header)
        
        -- Titre
        draw.SimpleText(storageInfo.name, "DermaLarge", w/2, 20, colors.text, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        
        -- Indicateur de verrouillage
        if isLocked then
            draw.SimpleText("🔒 VERROUILLÉ", "DermaDefault", w - 10, 10, colors.danger, TEXT_ALIGN_RIGHT)
        end
    end
    
    -- Bouton de fermeture
    local closeBtn = vgui.Create("DButton", frame)
    closeBtn:SetSize(30, 30)
    closeBtn:SetPos(frame:GetWide() - 35, 5)
    closeBtn:SetText("✕")
    closeBtn:SetFont("DermaLarge")
    closeBtn:SetTextColor(colors.text)
    closeBtn.Paint = function(self, w, h)
        local bgColor = self:IsHovered() and colors.danger or Color(0, 0, 0, 0)
        draw.RoundedBox(4, 0, 0, w, h, bgColor)
    end
    closeBtn.DoClick = function()
        ApocalypseStorage.CloseStorageMenu()
    end
    
    -- Informations du stockage
    local infoPanel = vgui.Create("DPanel", frame)
    infoPanel:SetSize(frame:GetWide() - 20, 80)
    infoPanel:SetPos(10, 50)
    infoPanel.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(60, 60, 60, 100))
        
        local currentWeight = 0
        for _, item in pairs(items) do
            currentWeight = currentWeight + (item.weight or 1)
        end
        
        -- Informations
        draw.SimpleText("Slots: " .. #items .. "/" .. storageInfo.slots, "DermaDefault", 10, 10, colors.text)
        draw.SimpleText("Poids: " .. currentWeight .. "/" .. storageInfo.maxWeight .. " kg", "DermaDefault", 10, 30, colors.text)
        draw.SimpleText(storageInfo.description, "DermaDefault", 10, 50, colors.textDark)
        
        -- Barre de progression du poids
        local weightPercent = currentWeight / storageInfo.maxWeight
        local barColor = weightPercent > 0.8 and colors.danger or (weightPercent > 0.6 and colors.accent or colors.success)
        draw.RoundedBox(2, 200, 15, 200, 10, Color(40, 40, 40, 255))
        draw.RoundedBox(2, 200, 15, 200 * weightPercent, 10, barColor)
    end
    
    -- Liste des objets
    local itemList = vgui.Create("DScrollPanel", frame)
    itemList:SetSize(frame:GetWide() - 20, 350)
    itemList:SetPos(10, 140)
    
    for i, item in pairs(items) do
        local itemPanel = vgui.Create("DPanel", itemList)
        itemPanel:SetSize(itemList:GetWide() - 20, 60)
        itemPanel:Dock(TOP)
        itemPanel:DockMargin(5, 5, 5, 0)
        
        itemPanel.Paint = function(self, w, h)
            local bgColor = self:IsHovered() and colors.buttonHover or colors.button
            draw.RoundedBox(4, 0, 0, w, h, bgColor)
            
            -- Nom de l'objet
            draw.SimpleText(item.class, "DermaDefault", 10, 10, colors.text)
            draw.SimpleText("Poids: " .. (item.weight or 1) .. " kg", "DermaDefault", 10, 30, colors.textDark)
            
            if item.ammo and item.ammo > 0 then
                draw.SimpleText("Munitions: " .. item.ammo, "DermaDefault", 200, 10, colors.textDark)
            end
            
            -- Bouton récupérer
            draw.RoundedBox(4, w - 80, 15, 70, 30, colors.accent)
            draw.SimpleText("Récupérer", "DermaDefault", w - 45, 30, colors.text, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        end
        
        itemPanel.OnMousePressed = function(self, keyCode)
            if keyCode == MOUSE_LEFT then
                net.Start("ApocalypseStorage_RetrieveItem")
                net.WriteEntity(ApocalypseStorage.CurrentStorage)
                net.WriteInt(i, 16)
                net.SendToServer()
            end
        end
    end
    
    -- Boutons d'action
    local actionPanel = vgui.Create("DPanel", frame)
    actionPanel:SetSize(frame:GetWide() - 20, 60)
    actionPanel:SetPos(10, frame:GetTall() - 70)
    actionPanel.Paint = function() end
    
    -- Bouton stocker l'arme actuelle
    local storeBtn = vgui.Create("DButton", actionPanel)
    storeBtn:SetSize(150, 40)
    storeBtn:SetPos(10, 10)
    storeBtn:SetText("Stocker l'arme")
    storeBtn:SetFont("DermaDefault")
    storeBtn:SetTextColor(colors.text)
    storeBtn.Paint = function(self, w, h)
        local bgColor = self:IsHovered() and Color(70, 120, 70, 255) or colors.success
        draw.RoundedBox(4, 0, 0, w, h, bgColor)
    end
    storeBtn.DoClick = function()
        net.Start("ApocalypseStorage_StoreItem")
        net.WriteEntity(ApocalypseStorage.CurrentStorage)
        net.SendToServer()
    end
    
    -- Bouton verrouiller/déverrouiller (si applicable)
    if storageInfo.lockable then
        local lockBtn = vgui.Create("DButton", actionPanel)
        lockBtn:SetSize(150, 40)
        lockBtn:SetPos(170, 10)
        lockBtn:SetText(isLocked and "Déverrouiller" or "Verrouiller")
        lockBtn:SetFont("DermaDefault")
        lockBtn:SetTextColor(colors.text)
        lockBtn.Paint = function(self, w, h)
            local bgColor = self:IsHovered() and Color(120, 70, 70, 255) or colors.accent
            draw.RoundedBox(4, 0, 0, w, h, bgColor)
        end
        lockBtn.DoClick = function()
            net.Start("ApocalypseStorage_ToggleLock")
            net.WriteEntity(ApocalypseStorage.CurrentStorage)
            net.SendToServer()
        end
    end
end

-- Fermer le menu de stockage
function ApocalypseStorage.CloseStorageMenu()
    if IsValid(ApocalypseStorage.StorageMenu) then
        ApocalypseStorage.StorageMenu:Remove()
        ApocalypseStorage.StorageMenu = nil
    end
    
    if IsValid(ApocalypseStorage.CurrentStorage) then
        net.Start("ApocalypseStorage_CloseMenu")
        net.WriteEntity(ApocalypseStorage.CurrentStorage)
        net.SendToServer()
        
        if ApocalypseStorage.Config.enableSounds then
            ApocalypseStorage.CurrentStorage:EmitSound(ApocalypseStorage.Sounds.close, ApocalypseStorage.Config.soundVolume)
        end
    end
    
    ApocalypseStorage.CurrentStorage = nil
end

-- Réseau : Recevoir la demande d'ouverture du menu depuis le serveur
net.Receive("ApocalypseStorage_OpenMenu", function()
    local storage = net.ReadEntity()

    if not IsValid(storage) then return end

    -- Stocker la référence du stockage actuel
    ApocalypseStorage.CurrentStorage = storage

    print("[Apocalypse Storage] Demande d'ouverture reçue pour:", storage:GetClass())
end)

-- Réseau : Mettre à jour l'inventaire
net.Receive("ApocalypseStorage_UpdateInventory", function()
    local storageID = net.ReadString()
    local items = net.ReadTable()
    local storageType = net.ReadString()
    local isLocked = net.ReadBool()

    print("[Apocalypse Storage] Réception de l'inventaire:", storageID, "Type:", storageType, "Items:", #items)

    ApocalypseStorage.CreateStorageMenu(storageID, items, storageType, isLocked)
end)

-- Fermer le menu avec Échap
hook.Add("OnPlayerChat", "ApocalypseStorage_CloseOnEscape", function(ply, text)
    if ply == LocalPlayer() and text == "!close" and IsValid(ApocalypseStorage.StorageMenu) then
        ApocalypseStorage.CloseStorageMenu()
        return true
    end
end)

-- Fermer le menu quand le joueur s'éloigne
hook.Add("Think", "ApocalypseStorage_DistanceCheck", function()
    if IsValid(ApocalypseStorage.CurrentStorage) and IsValid(ApocalypseStorage.StorageMenu) then
        local distance = LocalPlayer():GetPos():Distance(ApocalypseStorage.CurrentStorage:GetPos())
        if distance > ApocalypseStorage.Config.maxStorageDistance then
            ApocalypseStorage.CloseStorageMenu()
        end
    end
end)

print("[Apocalypse Storage] Client-side loaded")
