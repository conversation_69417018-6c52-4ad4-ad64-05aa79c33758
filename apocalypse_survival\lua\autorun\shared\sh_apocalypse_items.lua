-- Définition des objets pour le système de survie apocalypse
ApocalypseSurvival = ApocalypseSurvival or {}
ApocalypseSurvival.Items = {}

-- Structure d'un objet :
-- {
--     name = "Nom affiché",
--     category = "food/medical/ammo/misc",
--     hunger_restore = 0,     -- Points de faim restaurés
--     thirst_restore = 0,     -- Points de soif restaurés
--     health_restore = 0,     -- Points de vie restaurés
--     stackable = true/false, -- Peut être empilé
--     max_stack = 1,          -- Nombre max par stack
--     consumable = true/false,-- Peut être consommé
--     icon = "path/to/icon"   -- Icône (optionnel)
-- }

-- NOURRITURE
ApocalypseSurvival.Items["canned_food"] = {
    name = "Conserve",
    category = "food",
    hunger_restore = 35,
    thirst_restore = 5,
    health_restore = 0,
    stackable = true,
    max_stack = 5,
    consumable = true,
    description = "Une conserve de nourriture. Restaure beaucoup de faim."
}

ApocalypseSurvival.Items["bread"] = {
    name = "Pain",
    category = "food", 
    hunger_restore = 20,
    thirst_restore = 0,
    health_restore = 0,
    stackable = true,
    max_stack = 3,
    consumable = true,
    description = "Du pain rassis mais encore comestible."
}

ApocalypseSurvival.Items["apple"] = {
    name = "Pomme",
    category = "food",
    hunger_restore = 15,
    thirst_restore = 10,
    health_restore = 0,
    stackable = true,
    max_stack = 8,
    consumable = true,
    description = "Une pomme fraîche. Restaure un peu de faim et de soif."
}

ApocalypseSurvival.Items["meat"] = {
    name = "Viande cuite",
    category = "food",
    hunger_restore = 40,
    thirst_restore = 0,
    health_restore = 5,
    stackable = true,
    max_stack = 4,
    consumable = true,
    description = "De la viande bien cuite. Très nourrissante."
}

-- BOISSONS
ApocalypseSurvival.Items["water_bottle"] = {
    name = "Bouteille d'eau",
    category = "food",
    hunger_restore = 0,
    thirst_restore = 50,
    health_restore = 0,
    stackable = true,
    max_stack = 6,
    consumable = true,
    description = "Une bouteille d'eau pure. Étanche bien la soif."
}

ApocalypseSurvival.Items["dirty_water"] = {
    name = "Eau sale",
    category = "food",
    hunger_restore = 0,
    thirst_restore = 25,
    health_restore = -5,
    stackable = true,
    max_stack = 8,
    consumable = true,
    description = "De l'eau douteuse. Étanche la soif mais peut rendre malade."
}

ApocalypseSurvival.Items["energy_drink"] = {
    name = "Boisson énergisante",
    category = "food",
    hunger_restore = 5,
    thirst_restore = 30,
    health_restore = 10,
    stackable = true,
    max_stack = 4,
    consumable = true,
    description = "Une boisson énergisante. Restaure soif et un peu de santé."
}

-- OBJETS MÉDICAUX
ApocalypseSurvival.Items["bandage"] = {
    name = "Bandage",
    category = "medical",
    hunger_restore = 0,
    thirst_restore = 0,
    health_restore = 25,
    stackable = true,
    max_stack = 10,
    consumable = true,
    description = "Un bandage médical. Soigne les blessures légères."
}

ApocalypseSurvival.Items["medkit"] = {
    name = "Trousse médicale",
    category = "medical",
    hunger_restore = 0,
    thirst_restore = 0,
    health_restore = 75,
    stackable = true,
    max_stack = 3,
    consumable = true,
    description = "Une trousse médicale complète. Soigne efficacement."
}

ApocalypseSurvival.Items["painkillers"] = {
    name = "Antidouleurs",
    category = "medical",
    hunger_restore = 0,
    thirst_restore = 0,
    health_restore = 15,
    stackable = true,
    max_stack = 15,
    consumable = true,
    description = "Des antidouleurs. Soulage un peu la douleur."
}

-- MUNITIONS (exemples)
ApocalypseSurvival.Items["pistol_ammo"] = {
    name = "Munitions pistolet",
    category = "ammo",
    hunger_restore = 0,
    thirst_restore = 0,
    health_restore = 0,
    stackable = true,
    max_stack = 50,
    consumable = false,
    description = "Munitions pour pistolet. Calibre 9mm."
}

ApocalypseSurvival.Items["rifle_ammo"] = {
    name = "Munitions fusil",
    category = "ammo",
    hunger_restore = 0,
    thirst_restore = 0,
    health_restore = 0,
    stackable = true,
    max_stack = 30,
    consumable = false,
    description = "Munitions pour fusil d'assaut."
}

ApocalypseSurvival.Items["shotgun_ammo"] = {
    name = "Cartouches fusil à pompe",
    category = "ammo",
    hunger_restore = 0,
    thirst_restore = 0,
    health_restore = 0,
    stackable = true,
    max_stack = 20,
    consumable = false,
    description = "Cartouches pour fusil à pompe."
}

-- OBJETS DIVERS
ApocalypseSurvival.Items["flashlight"] = {
    name = "Lampe torche",
    category = "misc",
    hunger_restore = 0,
    thirst_restore = 0,
    health_restore = 0,
    stackable = false,
    max_stack = 1,
    consumable = false,
    description = "Une lampe torche fonctionnelle."
}

ApocalypseSurvival.Items["rope"] = {
    name = "Corde",
    category = "misc",
    hunger_restore = 0,
    thirst_restore = 0,
    health_restore = 0,
    stackable = true,
    max_stack = 5,
    consumable = false,
    description = "Une corde solide. Peut être utile."
}

ApocalypseSurvival.Items["scrap_metal"] = {
    name = "Ferraille",
    category = "misc",
    hunger_restore = 0,
    thirst_restore = 0,
    health_restore = 0,
    stackable = true,
    max_stack = 10,
    consumable = false,
    description = "Des morceaux de métal. Utile pour le crafting."
}

-- Fonction pour obtenir un objet
function ApocalypseSurvival.GetItem(itemId)
    return ApocalypseSurvival.Items[itemId]
end

-- Fonction pour obtenir tous les objets d'une catégorie
function ApocalypseSurvival.GetItemsByCategory(category)
    local items = {}
    for id, item in pairs(ApocalypseSurvival.Items) do
        if item.category == category then
            items[id] = item
        end
    end
    return items
end
