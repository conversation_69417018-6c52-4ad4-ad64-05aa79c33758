-- Apocalypse Storage System - Shared
-- Configuration et fonctions partagées

ApocalypseStorage = ApocalypseStorage or {}

-- Configuration des types de stockage
ApocalypseStorage.StorageTypes = {
    ["backpack"] = {
        name = "Sac à dos",
        slots = 12,
        maxWeight = 50,
        model = "models/props_c17/suitcase_passenger_physics.mdl",
        portable = true,
        lockable = false,
        description = "Stockage portable limité pour les objets essentiels"
    },
    ["locker"] = {
        name = "Casier métallique",
        slots = 20,
        maxWeight = 100,
        model = "models/props_c17/lockers001a.mdl",
        portable = false,
        lockable = true,
        description = "Casier personnel sécurisé"
    },
    ["safe"] = {
        name = "Coffre-fort",
        slots = 30,
        maxWeight = 200,
        model = "models/props_c17/FurnitureDresser001a.mdl",
        portable = false,
        lockable = true,
        description = "Stockage sécurisé haute capacité"
    },
    ["crate"] = {
        name = "Caisse de survie",
        slots = 15,
        maxWeight = 75,
        model = "models/props_junk/wood_crate001a.mdl",
        portable = false,
        lockable = false,
        description = "Stockage temporaire pour matériel de survie"
    },
    ["cabinet"] = {
        name = "Armoire",
        slots = 25,
        maxWeight = 150,
        model = "models/props_c17/FurnitureWardrobeCloset001a.mdl",
        portable = false,
        lockable = true,
        description = "Grande armoire pour stockage général"
    }
}

-- Configuration générale
ApocalypseStorage.Config = {
    maxStorageDistance = 100, -- Distance maximale pour accéder au stockage
    saveInterval = 30, -- Intervalle de sauvegarde en secondes
    lockDuration = 300, -- Durée du verrouillage en secondes
    soundVolume = 0.5,
    enableSounds = true,
    enableParticles = true
}

-- Sons du système
ApocalypseStorage.Sounds = {
    open = "doors/door_metal_thin_open1.wav",
    close = "doors/door_metal_thin_close2.wav",
    lock = "buttons/lever7.wav",
    unlock = "buttons/lever8.wav",
    error = "buttons/button10.wav",
    pickup = "items/ammo_pickup.wav"
}

-- Fonction pour obtenir les informations d'un type de stockage
function ApocalypseStorage.GetStorageInfo(storageType)
    return ApocalypseStorage.StorageTypes[storageType] or ApocalypseStorage.StorageTypes["backpack"]
end

-- Fonction pour calculer le poids d'un objet
function ApocalypseStorage.GetItemWeight(item)
    if not item or not IsValid(item) then return 0 end
    
    -- Poids basé sur la classe de l'entité
    local weights = {
        ["weapon_"] = 5,
        ["item_"] = 2,
        ["prop_"] = 10,
        ["ent_"] = 3
    }
    
    local class = item:GetClass()
    for prefix, weight in pairs(weights) do
        if string.StartWith(class, prefix) then
            return weight
        end
    end
    
    return 1 -- Poids par défaut
end

-- Fonction pour vérifier si un joueur peut accéder au stockage
function ApocalypseStorage.CanPlayerAccess(ply, storage)
    if not IsValid(ply) or not IsValid(storage) then return false end
    
    local distance = ply:GetPos():Distance(storage:GetPos())
    if distance > ApocalypseStorage.Config.maxStorageDistance then
        return false
    end
    
    -- Vérifier si le stockage est verrouillé
    if storage:GetLocked() and storage:GetOwner() != ply then
        return false
    end
    
    return true
end

-- Messages réseau (déclarés dans l'entité de base)

print("[Apocalypse Storage] Shared configuration loaded")
