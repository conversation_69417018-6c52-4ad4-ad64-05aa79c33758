-- Système de ramassage avec menu de choix - Côté client
ApocalypseSurvival = ApocalypseSurvival or {}
ApocalypseSurvival.PickupMenu = nil
ApocalypseSurvival.CurrentTarget = nil
ApocalypseSurvival.LastTarget = nil

-- Vérifier que la configuration est chargée
if not ApocalypseSurvival.Config then
    include("autorun/shared/sh_apocalypse_config.lua")
end
if not ApocalypseSurvival.Items then
    include("autorun/shared/sh_apocalypse_items.lua")
end

-- Couleurs pour l'interface de ramassage
local PICKUP_BG_COLOR = Color(20, 20, 20, 240)
local PICKUP_BORDER_COLOR = Color(180, 140, 80, 255)
local PICKUP_BUTTON_COLOR = Color(60, 60, 60, 220)
local PICKUP_BUTTON_HOVER = Color(100, 100, 100, 240)
local PICKUP_TEXT_COLOR = Color(255, 255, 255, 255)

-- Fonction pour vérifier si une entité peut être ramassée
function ApocalypseSurvival.CanPickupEntity(ent)
    if not <PERSON><PERSON>alid(ent) then return false end
    
    -- Vérifier si c'est une arme
    if ent:IsWeapon() then
        local weaponClass = ent:GetClass()
        
        -- Ignorer les armes système
        local ignoredWeapons = {
            "weapon_physgun",
            "gmod_tool",
            "weapon_physcannon"
        }
        
        for _, ignored in ipairs(ignoredWeapons) do
            if weaponClass == ignored then
                return false
            end
        end
        
        return true
    end
    
    -- Vérifier si c'est un objet de survie
    local itemId = ent:GetNWString("ApocalypseSurvival_ItemID", "")
    if itemId ~= "" and ApocalypseSurvival.GetItem(itemId) then
        return true
    end
    
    return false
end

-- Fonction pour obtenir les informations d'un objet
function ApocalypseSurvival.GetEntityInfo(ent)
    if not IsValid(ent) then return nil end
    
    -- Si c'est une arme
    if ent:IsWeapon() then
        local weaponClass = ent:GetClass()
        
        -- Trouver l'item correspondant
        for id, item in pairs(ApocalypseSurvival.Items) do
            if item.weapon_class == weaponClass then
                return {
                    name = item.name,
                    description = item.description or "",
                    type = "weapon",
                    class = weaponClass,
                    itemId = id
                }
            end
        end
        
        -- Arme non définie dans les items
        return {
            name = weaponClass,
            description = "Arme",
            type = "weapon",
            class = weaponClass,
            itemId = nil
        }
    end
    
    -- Si c'est un objet de survie
    local itemId = ent:GetNWString("ApocalypseSurvival_ItemID", "")
    if itemId ~= "" then
        local item = ApocalypseSurvival.GetItem(itemId)
        if item then
            local quantity = ent:GetNWInt("ApocalypseSurvival_Quantity", 1)
            return {
                name = item.name,
                description = item.description or "",
                type = item.category,
                quantity = quantity,
                itemId = itemId
            }
        end
    end
    
    return nil
end

-- Créer le menu de ramassage
function ApocalypseSurvival.CreatePickupMenu(ent, info)
    if IsValid(ApocalypseSurvival.PickupMenu) then
        ApocalypseSurvival.PickupMenu:Remove()
    end
    
    local scrW, scrH = ScrW(), ScrH()
    local menuW, menuH = 300, 150
    
    -- Panel principal
    local menu = vgui.Create("DPanel")
    menu:SetSize(menuW, menuH)
    menu:SetPos(scrW/2 - menuW/2, scrH/2 - menuH/2)
    menu:MakePopup()
    menu:SetKeyboardInputEnabled(false)
    
    ApocalypseSurvival.PickupMenu = menu
    
    menu.Paint = function(self, w, h)
        -- Fond
        surface.SetDrawColor(PICKUP_BG_COLOR)
        surface.DrawRect(0, 0, w, h)
        
        -- Bordure
        surface.SetDrawColor(PICKUP_BORDER_COLOR)
        surface.DrawOutlinedRect(0, 0, w, h, 2)
        
        -- Titre
        local title = info.quantity and (info.name .. " x" .. info.quantity) or info.name
        draw.SimpleText(title, "DermaLarge", w/2, 15, PICKUP_TEXT_COLOR, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        
        -- Description
        if info.description ~= "" then
            draw.SimpleText(info.description, "DermaDefault", w/2, 35, Color(200, 200, 200, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        end
    end
    
    -- Boutons d'action
    local buttonY = 60
    local buttonW = 120
    local buttonH = 30
    local buttonSpacing = 10
    
    if info.type == "weapon" then
        -- Bouton "Prendre"
        local takeBtn = vgui.Create("DButton", menu)
        takeBtn:SetPos(menuW/2 - buttonW - buttonSpacing/2, buttonY)
        takeBtn:SetSize(buttonW, buttonH)
        takeBtn:SetText("Prendre")
        takeBtn:SetFont("DermaDefaultBold")
        takeBtn:SetTextColor(PICKUP_TEXT_COLOR)
        
        takeBtn.Paint = function(self, w, h)
            local color = self:IsHovered() and PICKUP_BUTTON_HOVER or PICKUP_BUTTON_COLOR
            surface.SetDrawColor(color)
            surface.DrawRect(0, 0, w, h)
            surface.SetDrawColor(PICKUP_BORDER_COLOR)
            surface.DrawOutlinedRect(0, 0, w, h)
        end
        
        takeBtn.DoClick = function()
            net.Start("ApocalypseSurvival_PickupItem")
            net.WriteInt(ent:EntIndex(), 16)
            net.WriteString("take")
            net.SendToServer()
            ApocalypseSurvival.ClosePickupMenu()
        end
        
        -- Bouton "Équiper"
        local equipBtn = vgui.Create("DButton", menu)
        equipBtn:SetPos(menuW/2 + buttonSpacing/2, buttonY)
        equipBtn:SetSize(buttonW, buttonH)
        equipBtn:SetText("Équiper")
        equipBtn:SetFont("DermaDefaultBold")
        equipBtn:SetTextColor(PICKUP_TEXT_COLOR)
        
        equipBtn.Paint = function(self, w, h)
            local color = self:IsHovered() and PICKUP_BUTTON_HOVER or PICKUP_BUTTON_COLOR
            surface.SetDrawColor(color)
            surface.DrawRect(0, 0, w, h)
            surface.SetDrawColor(PICKUP_BORDER_COLOR)
            surface.DrawOutlinedRect(0, 0, w, h)
        end
        
        equipBtn.DoClick = function()
            net.Start("ApocalypseSurvival_PickupItem")
            net.WriteInt(ent:EntIndex(), 16)
            net.WriteString("equip")
            net.SendToServer()
            ApocalypseSurvival.ClosePickupMenu()
        end
    else
        -- Bouton "Prendre" centré pour les objets
        local takeBtn = vgui.Create("DButton", menu)
        takeBtn:SetPos(menuW/2 - buttonW/2, buttonY)
        takeBtn:SetSize(buttonW, buttonH)
        takeBtn:SetText("Prendre")
        takeBtn:SetFont("DermaDefaultBold")
        takeBtn:SetTextColor(PICKUP_TEXT_COLOR)
        
        takeBtn.Paint = function(self, w, h)
            local color = self:IsHovered() and PICKUP_BUTTON_HOVER or PICKUP_BUTTON_COLOR
            surface.SetDrawColor(color)
            surface.DrawRect(0, 0, w, h)
            surface.SetDrawColor(PICKUP_BORDER_COLOR)
            surface.DrawOutlinedRect(0, 0, w, h)
        end
        
        takeBtn.DoClick = function()
            net.Start("ApocalypseSurvival_PickupItem")
            net.WriteInt(ent:EntIndex(), 16)
            net.WriteString("take")
            net.SendToServer()
            ApocalypseSurvival.ClosePickupMenu()
        end
    end
    
    -- Bouton "Annuler"
    local cancelBtn = vgui.Create("DButton", menu)
    cancelBtn:SetPos(menuW/2 - 50, buttonY + 40)
    cancelBtn:SetSize(100, 25)
    cancelBtn:SetText("Annuler")
    cancelBtn:SetFont("DermaDefault")
    cancelBtn:SetTextColor(Color(200, 200, 200, 255))
    
    cancelBtn.Paint = function(self, w, h)
        local color = self:IsHovered() and Color(100, 50, 50, 200) or Color(80, 80, 80, 200)
        surface.SetDrawColor(color)
        surface.DrawRect(0, 0, w, h)
        surface.SetDrawColor(PICKUP_BORDER_COLOR)
        surface.DrawOutlinedRect(0, 0, w, h)
    end
    
    cancelBtn.DoClick = function()
        ApocalypseSurvival.ClosePickupMenu()
    end
    
    -- Fermer automatiquement après 5 secondes
    timer.Simple(5, function()
        if IsValid(menu) then
            ApocalypseSurvival.ClosePickupMenu()
        end
    end)
end

-- Fermer le menu de ramassage
function ApocalypseSurvival.ClosePickupMenu()
    if IsValid(ApocalypseSurvival.PickupMenu) then
        ApocalypseSurvival.PickupMenu:Remove()
        ApocalypseSurvival.PickupMenu = nil
    end
    ApocalypseSurvival.CurrentTarget = nil
end

-- Détection des objets visés
hook.Add("Think", "ApocalypseSurvival_PickupDetection", function()
    local ply = LocalPlayer()
    if not IsValid(ply) then return end
    
    -- Ne pas détecter si l'inventaire est ouvert
    if ApocalypseSurvival.InventoryOpen then return end
    
    local trace = ply:GetEyeTrace()
    local ent = trace.Entity
    
    -- Vérifier si on vise un objet ramassable
    if IsValid(ent) and trace.HitPos:Distance(ply:GetPos()) <= 100 then
        if ApocalypseSurvival.CanPickupEntity(ent) then
            ApocalypseSurvival.CurrentTarget = ent
        else
            ApocalypseSurvival.CurrentTarget = nil
        end
    else
        ApocalypseSurvival.CurrentTarget = nil
    end
    
    -- Fermer le menu si on ne vise plus l'objet
    if ApocalypseSurvival.CurrentTarget ~= ApocalypseSurvival.LastTarget then
        if IsValid(ApocalypseSurvival.PickupMenu) and not ApocalypseSurvival.CurrentTarget then
            ApocalypseSurvival.ClosePickupMenu()
        end
        ApocalypseSurvival.LastTarget = ApocalypseSurvival.CurrentTarget
    end
end)

-- Affichage du HUD de ramassage
hook.Add("HUDPaint", "ApocalypseSurvival_PickupHUD", function()
    if ApocalypseSurvival.CurrentTarget and not ApocalypseSurvival.InventoryOpen then
        local ent = ApocalypseSurvival.CurrentTarget
        local info = ApocalypseSurvival.GetEntityInfo(ent)
        
        if info then
            local scrW, scrH = ScrW(), ScrH()
            
            -- Afficher le nom de l'objet au centre de l'écran
            local text = info.quantity and (info.name .. " x" .. info.quantity) or info.name
            draw.SimpleText(text, "DermaLarge", scrW/2, scrH/2 + 50, Color(255, 255, 255, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
            
            -- Instructions
            draw.SimpleText("Appuyez sur [E] pour ramasser", "DermaDefault", scrW/2, scrH/2 + 70, Color(200, 200, 200, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        end
    end
end)

-- Gestion de la touche E
hook.Add("PlayerButtonDown", "ApocalypseSurvival_PickupKey", function(ply, button)
    if ply ~= LocalPlayer() then return end
    
    if button == KEY_E and ApocalypseSurvival.CurrentTarget and not ApocalypseSurvival.InventoryOpen then
        local ent = ApocalypseSurvival.CurrentTarget
        local info = ApocalypseSurvival.GetEntityInfo(ent)
        
        if info then
            ApocalypseSurvival.CreatePickupMenu(ent, info)
        end
    end
end)
