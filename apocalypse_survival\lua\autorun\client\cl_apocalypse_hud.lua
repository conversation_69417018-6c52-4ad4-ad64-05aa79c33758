-- Interface HUD pour le système de survie apocalypse - Côté client
ApocalypseSurvival = ApocalypseSurvival or {}

-- Vérifier que la configuration est chargée
if not ApocalypseSurvival.Config then
    include("autorun/shared/sh_apocalypse_config.lua")
end
if not ApocalypseSurvival.Items then
    include("autorun/shared/sh_apocalypse_items.lua")
end

ApocalypseSurvival.ClientData = {
    hunger = 100,
    thirst = 100,
    inventory = {
        weapons = {},
        ammo = {},
        food = {},
        medical = {},
        misc = {}
    }
}

-- Variables pour les effets visuels
local lastHunger = 100
local lastThirst = 100
local screenEffectAlpha = 0
local warningTime = 0

-- Recevoir les données du serveur
net.Receive("ApocalypseSurvival_UpdateData", function()
    ApocalypseSurvival.ClientData.hunger = net.ReadFloat()
    ApocalypseSurvival.ClientData.thirst = net.ReadFloat()
    ApocalypseSurvival.ClientData.inventory = net.ReadTable()
end)

-- Fonction pour dessiner une barre de progression
local function DrawProgressBar(x, y, w, h, value, maxValue, color, bgColor, text)
    -- Fond
    surface.SetDrawColor(bgColor)
    surface.DrawRect(x, y, w, h)
    
    -- Barre de progression
    local progress = math.Clamp(value / maxValue, 0, 1)
    surface.SetDrawColor(color)
    surface.DrawRect(x + 2, y + 2, (w - 4) * progress, h - 4)
    
    -- Bordure
    surface.SetDrawColor(255, 255, 255, 100)
    surface.DrawOutlinedRect(x, y, w, h)
    
    -- Texte
    if text then
        draw.SimpleText(text, "DermaDefault", x + w/2, y + h/2, ApocalypseSurvival.Config.Colors.text, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end
    
    -- Valeur numérique
    local valueText = math.floor(value) .. "/" .. maxValue
    draw.SimpleText(valueText, "DermaDefaultBold", x + w + 10, y + h/2, ApocalypseSurvival.Config.Colors.text, TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)
end

-- Fonction pour obtenir la couleur selon la valeur
local function GetStatusColor(value, maxValue, goodColor, warningColor, criticalColor)
    local percentage = value / maxValue
    if percentage > 0.5 then
        return goodColor
    elseif percentage > 0.2 then
        return warningColor
    else
        return criticalColor
    end
end

-- Dessiner le HUD principal
hook.Add("HUDPaint", "ApocalypseSurvival_DrawHUD", function()
    local ply = LocalPlayer()
    if not IsValid(ply) or not ply:Alive() then return end

    -- Vérifier que la configuration est disponible
    if not ApocalypseSurvival.Config then return end

    local scrW, scrH = ScrW(), ScrH()
    local data = ApocalypseSurvival.ClientData
    
    -- Position du HUD (coin supérieur gauche)
    local hudX = 20
    local hudY = 20
    local barWidth = 200
    local barHeight = 25
    local spacing = 35
    
    -- Fond du HUD
    surface.SetDrawColor(ApocalypseSurvival.Config.Colors.background)
    surface.DrawRect(hudX - 10, hudY - 10, barWidth + 120, spacing * 3)
    
    -- Barre de faim
    local hungerColor = GetStatusColor(data.hunger, ApocalypseSurvival.Config.MaxHunger,
        ApocalypseSurvival.Config.Colors.hunger_good,
        ApocalypseSurvival.Config.Colors.hunger_warning,
        ApocalypseSurvival.Config.Colors.hunger_critical)
    
    DrawProgressBar(hudX, hudY, barWidth, barHeight, data.hunger, ApocalypseSurvival.Config.MaxHunger,
        hungerColor, Color(50, 50, 50, 200), "FAIM")
    
    -- Barre de soif
    local thirstColor = GetStatusColor(data.thirst, ApocalypseSurvival.Config.MaxThirst,
        ApocalypseSurvival.Config.Colors.thirst_good,
        ApocalypseSurvival.Config.Colors.thirst_warning,
        ApocalypseSurvival.Config.Colors.thirst_critical)
    
    DrawProgressBar(hudX, hudY + spacing, barWidth, barHeight, data.thirst, ApocalypseSurvival.Config.MaxThirst,
        thirstColor, Color(50, 50, 50, 200), "SOIF")
    
    -- Barre de vie
    local health = ply:Health()
    local healthColor = GetStatusColor(health, ApocalypseSurvival.Config.MaxHealth,
        Color(0, 255, 0, 200), Color(255, 255, 0, 200), Color(255, 0, 0, 200))
    
    DrawProgressBar(hudX, hudY + spacing * 2, barWidth, barHeight, health, ApocalypseSurvival.Config.MaxHealth,
        healthColor, Color(50, 50, 50, 200), "VIE")
    
    -- Effets visuels pour les états critiques
    if ApocalypseSurvival.Config.Effects.enable_screen_effects then
        local effectIntensity = 0
        
        -- Effet de faim critique
        if data.hunger <= ApocalypseSurvival.Config.CriticalHunger then
            effectIntensity = math.max(effectIntensity, (ApocalypseSurvival.Config.CriticalHunger - data.hunger) / ApocalypseSurvival.Config.CriticalHunger)
        end
        
        -- Effet de soif critique
        if data.thirst <= ApocalypseSurvival.Config.CriticalThirst then
            effectIntensity = math.max(effectIntensity, (ApocalypseSurvival.Config.CriticalThirst - data.thirst) / ApocalypseSurvival.Config.CriticalThirst)
        end
        
        if effectIntensity > 0 then
            screenEffectAlpha = math.min(255, screenEffectAlpha + FrameTime() * 100)
            
            -- Effet de vignette rouge
            local alpha = math.sin(CurTime() * 3) * 50 + 50
            surface.SetDrawColor(255, 0, 0, alpha * effectIntensity)
            
            -- Bordures de l'écran
            surface.DrawRect(0, 0, scrW, 20) -- Haut
            surface.DrawRect(0, scrH - 20, scrW, 20) -- Bas
            surface.DrawRect(0, 0, 20, scrH) -- Gauche
            surface.DrawRect(scrW - 20, 0, 20, scrH) -- Droite
            
            -- Effet de pulsation au centre
            local pulseAlpha = math.sin(CurTime() * 5) * 30 + 30
            surface.SetDrawColor(255, 0, 0, pulseAlpha * effectIntensity * 0.3)
            surface.DrawRect(0, 0, scrW, scrH)
        else
            screenEffectAlpha = math.max(0, screenEffectAlpha - FrameTime() * 50)
        end
    end
    
    -- Messages d'avertissement
    if data.hunger <= ApocalypseSurvival.Config.CriticalHunger or data.thirst <= ApocalypseSurvival.Config.CriticalThirst then
        warningTime = warningTime + FrameTime()
        
        if warningTime > 3 then -- Afficher un message toutes les 3 secondes
            local message = ""
            if data.hunger <= 10 then
                message = ApocalypseSurvival.Config.Messages.hunger_critical
            elseif data.thirst <= 10 then
                message = ApocalypseSurvival.Config.Messages.thirst_critical
            end
            
            if message ~= "" then
                local textAlpha = math.sin(CurTime() * 8) * 100 + 155
                draw.SimpleText(message, "DermaLarge", scrW/2, scrH/2 + 100, 
                    Color(255, 255, 255, textAlpha), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
            end
            
            if warningTime > 4 then
                warningTime = 0
            end
        end
    else
        warningTime = 0
    end
    
    -- Indicateur d'inventaire (coin supérieur droit)
    local invX = scrW - 250
    local invY = 20
    
    surface.SetDrawColor(ApocalypseSurvival.Config.Colors.background)
    surface.DrawRect(invX, invY, 230, 120)
    
    draw.SimpleText("INVENTAIRE", "DermaDefaultBold", invX + 115, invY + 10, 
        ApocalypseSurvival.Config.Colors.text, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    
    local yOffset = 30
    for category, slots in pairs(ApocalypseSurvival.Config.InventorySlots) do
        local used = #(data.inventory[category] or {})
        local color = used >= slots and Color(255, 0, 0, 255) or Color(255, 255, 255, 255)
        
        local categoryName = string.upper(category)
        if category == "weapons" then categoryName = "ARMES"
        elseif category == "ammo" then categoryName = "MUNITIONS"
        elseif category == "food" then categoryName = "NOURRITURE"
        elseif category == "medical" then categoryName = "MÉDICAL"
        elseif category == "misc" then categoryName = "DIVERS"
        end
        
        draw.SimpleText(categoryName .. ": " .. used .. "/" .. slots, "DermaDefault", 
            invX + 10, invY + yOffset, color, TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)
        yOffset = yOffset + 18
    end
    
    -- Instructions
    draw.SimpleText("Appuyez sur [I] pour ouvrir l'inventaire", "DermaDefault", 
        invX + 10, invY + 100, Color(200, 200, 200, 255), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)
end)

-- Cacher les éléments HUD par défaut qui interfèrent
hook.Add("HUDShouldDraw", "ApocalypseSurvival_HideHUD", function(name)
    if name == "CHudHealth" or name == "CHudBattery" then
        return false
    end
end)
