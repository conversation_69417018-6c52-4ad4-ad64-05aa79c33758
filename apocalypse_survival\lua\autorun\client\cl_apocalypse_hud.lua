-- Interface HUD pour le système de survie apocalypse - Côté client
ApocalypseSurvival = ApocalypseSurvival or {}

-- Vérifier que la configuration est chargée
if not ApocalypseSurvival.Config then
    include("autorun/shared/sh_apocalypse_config.lua")
end
if not ApocalypseSurvival.Items then
    include("autorun/shared/sh_apocalypse_items.lua")
end

ApocalypseSurvival.ClientData = {
    hunger = 100,
    thirst = 100,
    inventory = {
        unified = {}  -- Inventaire unifié style DayZ
    }
}

-- Variables pour les effets visuels
local lastHunger = 100
local lastThirst = 100
local screenEffectAlpha = 0
local warningTime = 0

-- Recevoir les données du serveur
net.Receive("ApocalypseSurvival_UpdateData", function()
    ApocalypseSurvival.ClientData.hunger = net.ReadFloat()
    ApocalypseSurvival.ClientData.thirst = net.ReadFloat()
    ApocalypseSurvival.ClientData.inventory = net.ReadTable()
end)

-- Fonction pour dessiner une barre de progression style DayZ
local function DrawProgressBar(x, y, w, h, value, maxValue, color, bgColor, text, icon)
    -- Fond avec effet de profondeur
    surface.SetDrawColor(Color(bgColor.r - 20, bgColor.g - 20, bgColor.b - 20, bgColor.a))
    surface.DrawRect(x + 2, y + 2, w, h)
    surface.SetDrawColor(bgColor)
    surface.DrawRect(x, y, w, h)

    -- Barre de progression avec dégradé
    local progress = math.Clamp(value / maxValue, 0, 1)
    surface.SetDrawColor(color)
    surface.DrawRect(x + 3, y + 3, (w - 6) * progress, h - 6)

    -- Effet de brillance sur la barre
    if progress > 0 then
        surface.SetDrawColor(Color(color.r + 30, color.g + 30, color.b + 30, 100))
        surface.DrawRect(x + 3, y + 3, (w - 6) * progress, 2)
    end

    -- Bordure style militaire
    surface.SetDrawColor(Color(180, 140, 80, 255))
    surface.DrawOutlinedRect(x, y, w, h, 2)

    -- Icône et texte
    if icon then
        draw.SimpleText(icon, "DermaDefault", x - 20, y + h/2, Color(180, 140, 80, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end

    if text then
        draw.SimpleText(text, "DermaDefaultBold", x + 8, y + h/2, Color(255, 255, 255, 255), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)
    end

    -- Valeur numérique avec style DayZ
    local valueText = math.floor(value) .. "/" .. maxValue
    local percentage = math.floor((value / maxValue) * 100) .. "%"
    draw.SimpleText(valueText, "DermaDefaultBold", x + w + 10, y + h/2 - 5, Color(220, 220, 220, 255), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)
    draw.SimpleText(percentage, "DermaDefault", x + w + 10, y + h/2 + 8, Color(180, 140, 80, 255), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)
end

-- Fonction pour obtenir la couleur selon la valeur
local function GetStatusColor(value, maxValue, goodColor, warningColor, criticalColor)
    local percentage = value / maxValue
    if percentage > 0.5 then
        return goodColor
    elseif percentage > 0.2 then
        return warningColor
    else
        return criticalColor
    end
end

-- Dessiner le HUD principal
hook.Add("HUDPaint", "ApocalypseSurvival_DrawHUD", function()
    local ply = LocalPlayer()
    if not IsValid(ply) or not ply:Alive() then return end

    -- Vérifier que la configuration est disponible
    if not ApocalypseSurvival.Config then return end

    local scrW, scrH = ScrW(), ScrH()
    local data = ApocalypseSurvival.ClientData
    
    -- Position du HUD style DayZ (coin supérieur gauche)
    local hudX = 25
    local hudY = 25
    local barWidth = 220
    local barHeight = 20
    local spacing = 30

    -- Fond du HUD style militaire
    local hudBgWidth = barWidth + 140
    local hudBgHeight = spacing * 3 + 20

    surface.SetDrawColor(Color(20, 20, 20, 200))
    surface.DrawRect(hudX - 15, hudY - 15, hudBgWidth, hudBgHeight)

    -- Bordure dorée style DayZ
    surface.SetDrawColor(Color(180, 140, 80, 255))
    surface.DrawOutlinedRect(hudX - 15, hudY - 15, hudBgWidth, hudBgHeight, 2)

    -- Lignes décoratives
    surface.SetDrawColor(Color(180, 140, 80, 100))
    surface.DrawRect(hudX - 10, hudY - 5, hudBgWidth - 10, 1)
    surface.DrawRect(hudX - 10, hudY + hudBgHeight - 25, hudBgWidth - 10, 1)
    
    -- Barre de faim style DayZ
    local hungerColor = GetStatusColor(data.hunger, ApocalypseSurvival.Config.MaxHunger,
        ApocalypseSurvival.Config.Colors.hunger_good,
        ApocalypseSurvival.Config.Colors.hunger_warning,
        ApocalypseSurvival.Config.Colors.hunger_critical)

    DrawProgressBar(hudX, hudY, barWidth, barHeight, data.hunger, ApocalypseSurvival.Config.MaxHunger,
        hungerColor, Color(40, 40, 40, 220), "FAIM", "🍖")

    -- Barre de soif style DayZ
    local thirstColor = GetStatusColor(data.thirst, ApocalypseSurvival.Config.MaxThirst,
        ApocalypseSurvival.Config.Colors.thirst_good,
        ApocalypseSurvival.Config.Colors.thirst_warning,
        ApocalypseSurvival.Config.Colors.thirst_critical)

    DrawProgressBar(hudX, hudY + spacing, barWidth, barHeight, data.thirst, ApocalypseSurvival.Config.MaxThirst,
        thirstColor, Color(40, 40, 40, 220), "SOIF", "💧")

    -- Barre de vie style DayZ
    local health = ply:Health()
    local healthColor = GetStatusColor(health, ApocalypseSurvival.Config.MaxHealth,
        Color(100, 200, 100, 200), Color(200, 200, 100, 200), Color(200, 100, 100, 200))

    DrawProgressBar(hudX, hudY + spacing * 2, barWidth, barHeight, health, ApocalypseSurvival.Config.MaxHealth,
        healthColor, Color(40, 40, 40, 220), "VIE", "❤️")

    -- Affichage des munitions de l'arme actuelle
    local weapon = ply:GetActiveWeapon()
    if IsValid(weapon) then
        local weaponClass = weapon:GetClass()
        local weaponAmmo = weapon:Clip1()
        local maxClip = weapon:GetMaxClip1()

        -- Mapping des types de munitions
        local weaponAmmoTypes = {
            ["weapon_pistol"] = "pistol_ammo",
            ["weapon_357"] = "pistol_ammo",
            ["weapon_smg1"] = "rifle_ammo",
            ["weapon_ar2"] = "rifle_ammo",
            ["weapon_shotgun"] = "shotgun_ammo",
            ["weapon_crossbow"] = "rifle_ammo"
        }

        local ammoType = weaponAmmoTypes[weaponClass]
        if ammoType and maxClip > 0 then
            -- Compter les munitions dans l'inventaire
            local inventoryAmmo = 0
            for _, item in pairs(data.inventory.unified or {}) do
                local itemData = ApocalypseSurvival.GetItem(item.id)
                if itemData and itemData.category == "ammo" and item.id == ammoType then
                    inventoryAmmo = inventoryAmmo + item.quantity
                end
            end

            -- Afficher les munitions de l'arme
            local ammoY = hudY + spacing * 3

            -- Fond pour les munitions
            surface.SetDrawColor(Color(20, 20, 20, 200))
            surface.DrawRect(hudX - 15, ammoY - 10, barWidth + 140, 50)
            surface.SetDrawColor(Color(180, 140, 80, 255))
            surface.DrawOutlinedRect(hudX - 15, ammoY - 10, barWidth + 140, 50, 2)

            -- Munitions dans le chargeur
            local clipText = "CHARGEUR: " .. weaponAmmo .. "/" .. maxClip
            draw.SimpleText(clipText, "DermaDefaultBold", hudX, ammoY, Color(255, 255, 255, 255), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)

            -- Munitions dans l'inventaire
            local invText = "INVENTAIRE: " .. inventoryAmmo
            local invColor = inventoryAmmo > 0 and Color(100, 255, 100, 255) or Color(255, 100, 100, 255)
            draw.SimpleText(invText, "DermaDefaultBold", hudX, ammoY + 20, invColor, TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)

            -- Instructions de rechargement
            if inventoryAmmo > 0 and weaponAmmo < maxClip then
                draw.SimpleText("Appuyez sur [R] pour recharger", "DermaDefault", hudX + barWidth + 10, ammoY + 10, Color(200, 200, 200, 255), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)
            end
        end
    end
    
    -- Effets visuels pour les états critiques
    if ApocalypseSurvival.Config.Effects.enable_screen_effects then
        local effectIntensity = 0
        
        -- Effet de faim critique
        if data.hunger <= ApocalypseSurvival.Config.CriticalHunger then
            effectIntensity = math.max(effectIntensity, (ApocalypseSurvival.Config.CriticalHunger - data.hunger) / ApocalypseSurvival.Config.CriticalHunger)
        end
        
        -- Effet de soif critique
        if data.thirst <= ApocalypseSurvival.Config.CriticalThirst then
            effectIntensity = math.max(effectIntensity, (ApocalypseSurvival.Config.CriticalThirst - data.thirst) / ApocalypseSurvival.Config.CriticalThirst)
        end
        
        if effectIntensity > 0 then
            screenEffectAlpha = math.min(255, screenEffectAlpha + FrameTime() * 100)
            
            -- Effet de vignette rouge
            local alpha = math.sin(CurTime() * 3) * 50 + 50
            surface.SetDrawColor(255, 0, 0, alpha * effectIntensity)
            
            -- Bordures de l'écran
            surface.DrawRect(0, 0, scrW, 20) -- Haut
            surface.DrawRect(0, scrH - 20, scrW, 20) -- Bas
            surface.DrawRect(0, 0, 20, scrH) -- Gauche
            surface.DrawRect(scrW - 20, 0, 20, scrH) -- Droite
            
            -- Effet de pulsation au centre
            local pulseAlpha = math.sin(CurTime() * 5) * 30 + 30
            surface.SetDrawColor(255, 0, 0, pulseAlpha * effectIntensity * 0.3)
            surface.DrawRect(0, 0, scrW, scrH)
        else
            screenEffectAlpha = math.max(0, screenEffectAlpha - FrameTime() * 50)
        end
    end
    
    -- Messages d'avertissement
    if data.hunger <= ApocalypseSurvival.Config.CriticalHunger or data.thirst <= ApocalypseSurvival.Config.CriticalThirst then
        warningTime = warningTime + FrameTime()
        
        if warningTime > 3 then -- Afficher un message toutes les 3 secondes
            local message = ""
            if data.hunger <= 10 then
                message = ApocalypseSurvival.Config.Messages.hunger_critical
            elseif data.thirst <= 10 then
                message = ApocalypseSurvival.Config.Messages.thirst_critical
            end
            
            if message ~= "" then
                local textAlpha = math.sin(CurTime() * 8) * 100 + 155
                draw.SimpleText(message, "DermaLarge", scrW/2, scrH/2 + 100, 
                    Color(255, 255, 255, textAlpha), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
            end
            
            if warningTime > 4 then
                warningTime = 0
            end
        end
    else
        warningTime = 0
    end
    
    -- Indicateur d'inventaire style DayZ (coin supérieur droit)
    local invX = scrW - 280
    local invY = 25
    local invWidth = 250
    local invHeight = 140

    -- Fond style militaire
    surface.SetDrawColor(Color(20, 20, 20, 200))
    surface.DrawRect(invX, invY, invWidth, invHeight)

    -- Bordure dorée
    surface.SetDrawColor(Color(180, 140, 80, 255))
    surface.DrawOutlinedRect(invX, invY, invWidth, invHeight, 2)

    -- Header
    surface.SetDrawColor(Color(35, 35, 35, 255))
    surface.DrawRect(invX, invY, invWidth, 25)
    surface.SetDrawColor(Color(180, 140, 80, 255))
    surface.DrawOutlinedRect(invX, invY, invWidth, 25)

    draw.SimpleText("🎒 INVENTAIRE", "DermaDefaultBold", invX + invWidth/2, invY + 12,
        Color(255, 255, 255, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)

    -- Lignes décoratives
    surface.SetDrawColor(Color(180, 140, 80, 100))
    surface.DrawRect(invX + 5, invY + 30, invWidth - 10, 1)

    local yOffset = 40

    -- Inventaire unifié - affichage simplifié
    local used = #(data.inventory.unified or {})
    local maxSlots = ApocalypseSurvival.Config.InventorySlots.unified
    local fillPercent = used / maxSlots
    local color = fillPercent >= 1 and Color(200, 100, 100, 255) or
                 fillPercent >= 0.8 and Color(200, 200, 100, 255) or
                 Color(150, 200, 150, 255)

    -- Titre principal
    draw.SimpleText("📦 OBJETS TOTAUX", "DermaDefaultBold",
        invX + 10, invY + yOffset, Color(220, 220, 220, 255), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)

    -- Compteur principal
    draw.SimpleText(used .. "/" .. maxSlots, "DermaLarge",
        invX + invWidth - 50, invY + yOffset, color, TEXT_ALIGN_RIGHT, TEXT_ALIGN_CENTER)

    -- Barre de progression principale
    surface.SetDrawColor(Color(40, 40, 40, 200))
    surface.DrawRect(invX + 10, invY + yOffset + 20, invWidth - 20, 8)
    surface.SetDrawColor(color)
    surface.DrawRect(invX + 10, invY + yOffset + 20, (invWidth - 20) * fillPercent, 8)
    surface.SetDrawColor(Color(180, 140, 80, 255))
    surface.DrawOutlinedRect(invX + 10, invY + yOffset + 20, invWidth - 20, 8)

    yOffset = yOffset + 40

    -- Compteurs par type d'objet
    local itemTypes = {
        weapons = {name = "Armes", icon = "🔫", color = Color(200, 150, 100, 255)},
        food = {name = "Nourriture", icon = "🍖", color = Color(100, 200, 100, 255)},
        medical = {name = "Médical", icon = "🏥", color = Color(200, 100, 100, 255)},
        ammo = {name = "Munitions", icon = "📦", color = Color(150, 150, 200, 255)},
        misc = {name = "Divers", icon = "🔧", color = Color(200, 200, 100, 255)}
    }

    for category, info in pairs(itemTypes) do
        local count = 0
        for _, item in pairs(data.inventory.unified or {}) do
            local itemData = ApocalypseSurvival.GetItem(item.id)
            if itemData and itemData.category == category then
                count = count + item.quantity
            end
        end

        if count > 0 then
            draw.SimpleText(info.icon .. " " .. count, "DermaDefault",
                invX + 10, invY + yOffset, info.color, TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)
            yOffset = yOffset + 15
        end
    end

    -- Instructions
    surface.SetDrawColor(Color(180, 140, 80, 100))
    surface.DrawRect(invX + 5, invY + invHeight - 25, invWidth - 10, 1)
    draw.SimpleText("Appuyez sur [I] pour ouvrir", "DermaDefault",
        invX + invWidth/2, invY + invHeight - 12, Color(180, 140, 80, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
end)

-- Cacher les éléments HUD par défaut qui interfèrent
hook.Add("HUDShouldDraw", "ApocalypseSurvival_HideHUD", function(name)
    if name == "CHudHealth" or name == "CHudBattery" then
        return false
    end
end)
