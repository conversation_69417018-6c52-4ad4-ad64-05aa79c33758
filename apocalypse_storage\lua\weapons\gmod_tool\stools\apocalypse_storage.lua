-- Tool Gun pour placer les stockages apocalyptiques

TOOL.Category = "Apocalypse Storage"
TOOL.Name = "Placeur de Stockage"
TOOL.Command = nil
TOOL.ConfigName = ""

TOOL.ClientConVar["storage_type"] = "backpack"

if CLIENT then
    language.Add("tool.apocalypse_storage.name", "Placeur de Stockage Apocalyptique")
    language.Add("tool.apocalypse_storage.desc", "Place différents types de stockage pour le RP apocalyptique")
    language.Add("tool.apocalypse_storage.0", "Clic gauche: Placer un stockage. Clic droit: Supprimer un stockage.")
end

-- Liste des types de stockage disponibles
local storageTypes = {
    {name = "Sac à dos", value = "backpack", entity = "apoc_backpack"},
    {name = "Casier métallique", value = "locker", entity = "apoc_locker"},
    {name = "Coffre-fort", value = "safe", entity = "apoc_safe"},
    {name = "Caisse de survie", value = "crate", entity = "apoc_crate"},
    {name = "Armoire", value = "cabinet", entity = "apoc_cabinet"}
}

function TOOL:LeftClick(trace)
    if CLIENT then return true end

    local ply = self:GetOwner()
    if not IsValid(ply) then return false end

    local storageType = self:GetClientInfo("storage_type") or "backpack"
    local entityClass = nil

    -- Trouver la classe d'entité correspondante
    for _, storage in pairs(storageTypes) do
        if storage.value == storageType then
            entityClass = storage.entity
            break
        end
    end

    -- Si aucun type trouvé, utiliser le sac à dos par défaut
    if not entityClass then
        entityClass = "apoc_backpack"
        storageType = "backpack"
        ply:ChatPrint("[Stockage] Utilisation du type par défaut: Sac à dos")
    end
    
    -- Vérifier si on peut placer ici
    if not trace.Hit or trace.HitSky then
        return false
    end
    
    -- Créer l'entité de stockage
    local storage = ents.Create(entityClass)
    if not IsValid(storage) then
        ply:ChatPrint("[Stockage] Erreur lors de la création du stockage!")
        return false
    end
    
    -- Positionner l'entité
    local pos = trace.HitPos + trace.HitNormal * 10
    local ang = trace.HitNormal:Angle()
    ang:RotateAroundAxis(ang:Forward(), 90)
    
    storage:SetPos(pos)
    storage:SetAngles(ang)
    storage:Spawn()
    storage:Activate()
    
    -- Définir le propriétaire
    storage:SetOwner(ply)
    
    -- Ajouter à l'undo
    undo.Create("Apocalypse Storage")
    undo.AddEntity(storage)
    undo.SetPlayer(ply)
    undo.Finish()
    
    -- Ajouter au cleanup
    ply:AddCleanup("apocalypse_storage", storage)
    
    local storageInfo = ApocalypseStorage.GetStorageInfo(storageType)
    ply:ChatPrint("[Stockage] " .. storageInfo.name .. " placé avec succès!")
    
    return true
end

function TOOL:RightClick(trace)
    if CLIENT then return true end
    
    local ply = self:GetOwner()
    if not IsValid(ply) then return false end
    
    local ent = trace.Entity
    if not IsValid(ent) then return false end
    
    -- Vérifier si c'est un stockage apocalyptique
    if not string.StartWith(ent:GetClass(), "apoc_") then
        ply:ChatPrint("[Stockage] Ceci n'est pas un stockage apocalyptique!")
        return false
    end
    
    -- Vérifier les permissions
    if ent:GetOwner() != ply and not ply:IsAdmin() then
        ply:ChatPrint("[Stockage] Vous n'avez pas la permission de supprimer ce stockage!")
        return false
    end
    
    -- Faire tomber les objets avant de supprimer
    if ent.DropAllItems then
        ent:DropAllItems()
    end
    
    ent:Remove()
    ply:ChatPrint("[Stockage] Stockage supprimé!")
    
    return true
end

function TOOL:Reload(trace)
    return false
end

function TOOL.BuildCPanel(CPanel)
    -- Titre principal
    CPanel:AddControl("Header", {
        Description = "Placeur de Stockage Apocalyptique"
    })

    -- Instructions
    CPanel:AddControl("Label", {
        Text = "Clic gauche: Placer un stockage\nClic droit: Supprimer un stockage"
    })

    -- Sélecteur de type de stockage
    CPanel:AddControl("ComboBox", {
        Label = "Type de stockage",
        MenuButton = 1,
        Folder = "apocalypse_storage",
        Options = {
            ["Sac à dos"] = {apocalypse_storage_storage_type = "backpack"},
            ["Casier métallique"] = {apocalypse_storage_storage_type = "locker"},
            ["Coffre-fort"] = {apocalypse_storage_storage_type = "safe"},
            ["Caisse de survie"] = {apocalypse_storage_storage_type = "crate"},
            ["Armoire"] = {apocalypse_storage_storage_type = "cabinet"}
        }
    })

    -- Informations détaillées
    CPanel:AddControl("Header", {
        Description = "Types de stockage disponibles"
    })

    CPanel:AddControl("Label", {
        Text = "Sac à dos: 12 slots, 50kg, portable\nCasier: 20 slots, 100kg, verrouillable\nCoffre-fort: 30 slots, 200kg, très sécurisé\nCaisse: 15 slots, 75kg, temporaire\nArmoire: 25 slots, 150kg, grande capacité"
    })
end
