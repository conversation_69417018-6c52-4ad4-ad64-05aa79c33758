-- Interface d'inventaire pour le système de survie apocalypse - Côté client
ApocalypseSurvival = ApocalypseSurvival or {}

-- Vérifier que la configuration et les objets sont chargés
if not ApocalypseSurvival.Config then
    include("autorun/shared/sh_apocalypse_config.lua")
end
if not ApocalypseSurvival.Items then
    include("autorun/shared/sh_apocalypse_items.lua")
end

ApocalypseSurvival.InventoryOpen = false
ApocalypseSurvival.InventoryPanel = nil

-- Couleurs pour l'interface style DayZ
local SLOT_COLOR = Color(45, 45, 45, 230)
local SLOT_HOVER_COLOR = Color(70, 70, 70, 250)
local SLOT_BORDER_COLOR = Color(120, 120, 120, 255)
local SLOT_BORDER_HOVER = Color(200, 200, 200, 255)
local CATEGORY_COLOR = Color(25, 25, 25, 250)
local CATEGORY_HEADER = Color(35, 35, 35, 255)
local TEXT_COLOR = Color(220, 220, 220, 255)
local TEXT_HIGHLIGHT = Color(255, 255, 255, 255)
local BACKGROUND_COLOR = Color(20, 20, 20, 240)
local ACCENT_COLOR = Color(180, 140, 80, 255) -- Couleur dorée/beige style militaire

-- Fonction pour créer l'interface d'inventaire
function ApocalypseSurvival.CreateInventoryPanel()
    -- Vérifier que tout est chargé
    if not ApocalypseSurvival.Config or not ApocalypseSurvival.ClientData then
        return
    end

    if IsValid(ApocalypseSurvival.InventoryPanel) then
        ApocalypseSurvival.InventoryPanel:Remove()
    end
    
    local scrW, scrH = ScrW(), ScrH()
    local panelW, panelH = 1000, 700

    -- Panel principal style DayZ
    local frame = vgui.Create("DFrame")
    frame:SetSize(panelW, panelH)
    frame:SetPos((scrW - panelW) / 2, (scrH - panelH) / 2)
    frame:SetTitle("")
    frame:SetDraggable(true)
    frame:SetDeleteOnClose(false)
    frame:ShowCloseButton(false)
    frame:MakePopup()

    -- Style DayZ pour le frame
    frame.Paint = function(self, w, h)
        -- Fond principal
        surface.SetDrawColor(BACKGROUND_COLOR)
        surface.DrawRect(0, 0, w, h)

        -- Bordure extérieure
        surface.SetDrawColor(ACCENT_COLOR)
        surface.DrawOutlinedRect(0, 0, w, h, 2)

        -- Barre de titre style militaire
        surface.SetDrawColor(CATEGORY_HEADER)
        surface.DrawRect(0, 0, w, 35)

        surface.SetDrawColor(ACCENT_COLOR)
        surface.DrawOutlinedRect(0, 0, w, 35)

        -- Titre
        draw.SimpleText("INVENTAIRE DE SURVIE", "DermaLarge", w/2, 17, TEXT_HIGHLIGHT, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)

        -- Lignes décoratives
        surface.SetDrawColor(ACCENT_COLOR.r, ACCENT_COLOR.g, ACCENT_COLOR.b, 100)
        surface.DrawRect(10, 40, w-20, 1)
        surface.DrawRect(10, h-10, w-20, 1)
    end
    
    ApocalypseSurvival.InventoryPanel = frame
    
    -- Fonction de fermeture
    frame.OnClose = function()
        print("[Apocalypse Survival] Frame OnClose appelé")
        ApocalypseSurvival.InventoryOpen = false
        gui.EnableScreenClicker(false)
    end

    -- Empêcher la fermeture automatique par défaut
    frame.Think = function(self)
        if input.IsKeyDown(KEY_ESCAPE) and not self.EscapePressed then
            self.EscapePressed = true
            ApocalypseSurvival.CloseInventory()
        elseif not input.IsKeyDown(KEY_ESCAPE) then
            self.EscapePressed = false
        end
    end
    
    -- Bouton de fermeture style DayZ (plus grand et plus visible)
    local closeBtn = vgui.Create("DButton", frame)
    closeBtn:SetSize(40, 30)
    closeBtn:SetPos(panelW - 45, 3)
    closeBtn:SetText("")
    closeBtn:SetFont("DermaLarge")
    closeBtn:SetTextColor(Color(255, 255, 255, 255))
    closeBtn:SetCursor("hand")

    closeBtn.Paint = function(self, w, h)
        local isHovered = self:IsHovered()
        local color = isHovered and Color(200, 80, 80, 220) or Color(100, 100, 100, 200)
        local textColor = isHovered and Color(255, 255, 255, 255) or Color(220, 220, 220, 255)

        -- Fond du bouton
        surface.SetDrawColor(color)
        surface.DrawRect(0, 0, w, h)

        -- Bordure
        surface.SetDrawColor(ACCENT_COLOR)
        surface.DrawOutlinedRect(0, 0, w, h, 2)

        -- Effet hover
        if isHovered then
            surface.SetDrawColor(Color(255, 255, 255, 50))
            surface.DrawRect(2, 2, w-4, h-4)
        end

        -- Texte X centré
        draw.SimpleText("✕", "DermaLarge", w/2, h/2, textColor, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end

    closeBtn.DoClick = function(self)
        print("[Apocalypse Survival] Bouton de fermeture cliqué")
        ApocalypseSurvival.CloseInventory()
    end

    closeBtn.OnMousePressed = function(self, keyCode)
        if keyCode == MOUSE_LEFT then
            print("[Apocalypse Survival] Clic gauche sur bouton de fermeture")
            ApocalypseSurvival.CloseInventory()
        end
    end

    -- Panel de contenu style DayZ
    local content = vgui.Create("DPanel", frame)
    content:SetPos(10, 50)
    content:SetSize(panelW - 20, panelH - 70)
    content.Paint = function(self, w, h)
        -- Fond légèrement transparent
        surface.SetDrawColor(CATEGORY_COLOR)
        surface.DrawRect(0, 0, w, h)

        -- Bordure interne
        surface.SetDrawColor(ACCENT_COLOR.r, ACCENT_COLOR.g, ACCENT_COLOR.b, 150)
        surface.DrawOutlinedRect(0, 0, w, h)
    end
    
    -- Layout en colonnes style DayZ
    local leftPanel = vgui.Create("DPanel", content)
    leftPanel:SetPos(10, 10)
    leftPanel:SetSize((panelW - 60) / 2, panelH - 100)
    leftPanel.Paint = function(self, w, h)
        surface.SetDrawColor(CATEGORY_HEADER)
        surface.DrawRect(0, 0, w, 25)
        surface.SetDrawColor(ACCENT_COLOR)
        surface.DrawOutlinedRect(0, 0, w, h)
        draw.SimpleText("ÉQUIPEMENT", "DermaDefaultBold", w/2, 12, TEXT_HIGHLIGHT, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end

    local rightPanel = vgui.Create("DPanel", content)
    rightPanel:SetPos((panelW - 40) / 2, 10)
    rightPanel:SetSize((panelW - 60) / 2, panelH - 100)
    rightPanel.Paint = function(self, w, h)
        surface.SetDrawColor(CATEGORY_HEADER)
        surface.DrawRect(0, 0, w, 25)
        surface.SetDrawColor(ACCENT_COLOR)
        surface.DrawOutlinedRect(0, 0, w, h)
        draw.SimpleText("CONSOMMABLES", "DermaDefaultBold", w/2, 12, TEXT_HIGHLIGHT, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end

    -- Créer les catégories avec nouveau layout
    local leftCategories = {
        {id = "weapons", name = "ARMES", slots = ApocalypseSurvival.Config.InventorySlots.weapons, icon = "🔫"},
        {id = "ammo", name = "MUNITIONS", slots = ApocalypseSurvival.Config.InventorySlots.ammo, icon = "📦"},
        {id = "misc", name = "ÉQUIPEMENT", slots = ApocalypseSurvival.Config.InventorySlots.misc, icon = "🔧"}
    }

    local rightCategories = {
        {id = "food", name = "NOURRITURE", slots = ApocalypseSurvival.Config.InventorySlots.food, icon = "🍖"},
        {id = "medical", name = "MÉDICAL", slots = ApocalypseSurvival.Config.InventorySlots.medical, icon = "🏥"}
    }
    
    -- Fonction pour créer une section de catégorie style DayZ
    local function CreateCategorySection(parent, categories, startY)
        local yPos = startY

        for _, category in ipairs(categories) do
            -- Header de catégorie style militaire
            local categoryHeader = vgui.Create("DPanel", parent)
            categoryHeader:SetPos(10, yPos)
            categoryHeader:SetSize(parent:GetWide() - 20, 30)
            categoryHeader.Paint = function(self, w, h)
                -- Fond dégradé
                surface.SetDrawColor(CATEGORY_HEADER)
                surface.DrawRect(0, 0, w, h)

                -- Bordure
                surface.SetDrawColor(ACCENT_COLOR)
                surface.DrawOutlinedRect(0, 0, w, h)

                -- Ligne décorative
                surface.SetDrawColor(ACCENT_COLOR.r, ACCENT_COLOR.g, ACCENT_COLOR.b, 100)
                surface.DrawRect(5, h-2, w-10, 1)

                -- Texte avec icône
                local used = #(ApocalypseSurvival.ClientData.inventory[category.id] or {})
                local text = category.icon .. " " .. category.name .. " [" .. used .. "/" .. category.slots .. "]"
                draw.SimpleText(text, "DermaDefaultBold", 10, h/2, TEXT_HIGHLIGHT, TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)

                -- Indicateur de remplissage
                local fillPercent = used / category.slots
                local fillColor = fillPercent > 0.8 and Color(200, 100, 100, 200) or Color(100, 150, 100, 200)
                surface.SetDrawColor(fillColor)
                surface.DrawRect(w - 60, 8, 50 * fillPercent, 14)
                surface.SetDrawColor(ACCENT_COLOR)
                surface.DrawOutlinedRect(w - 60, 8, 50, 14)
            end

            yPos = yPos + 35
        
            -- Grille de slots style DayZ
            local slotsPerRow = math.floor((parent:GetWide() - 40) / 65)
            local slotSize = 55
            local slotSpacing = 8

            for i = 1, category.slots do
                local row = math.floor((i - 1) / slotsPerRow)
                local col = (i - 1) % slotsPerRow

                local slotX = 15 + col * (slotSize + slotSpacing)
                local slotY = yPos + row * (slotSize + slotSpacing)

                local slot = vgui.Create("DPanel", parent)
                slot:SetPos(slotX, slotY)
                slot:SetSize(slotSize, slotSize)
                slot:SetCursor("hand")
            
                -- Données du slot
                local slotData = (ApocalypseSurvival.ClientData.inventory[category.id] or {})[i]
                local item = slotData and ApocalypseSurvival.GetItem(slotData.id)

                -- Style DayZ pour les slots
                slot.Paint = function(self, w, h)
                    local isHovered = self:IsHovered()
                    local borderColor = isHovered and SLOT_BORDER_HOVER or SLOT_BORDER_COLOR
                    local bgColor = isHovered and SLOT_HOVER_COLOR or SLOT_COLOR

                    -- Fond du slot avec effet de profondeur
                    surface.SetDrawColor(Color(bgColor.r - 10, bgColor.g - 10, bgColor.b - 10, bgColor.a))
                    surface.DrawRect(2, 2, w-2, h-2)
                    surface.SetDrawColor(bgColor)
                    surface.DrawRect(0, 0, w-2, h-2)

                    -- Bordure avec effet 3D
                    surface.SetDrawColor(borderColor)
                    surface.DrawOutlinedRect(0, 0, w, h, 2)

                    -- Grille interne style DayZ
                    if not item then
                        surface.SetDrawColor(borderColor.r, borderColor.g, borderColor.b, 50)
                        for x = 10, w-10, 10 do
                            surface.DrawLine(x, 5, x, h-5)
                        end
                        for y = 10, h-10, 10 do
                            surface.DrawLine(5, y, w-5, y)
                        end
                    end

                    -- Dessiner l'objet s'il existe
                    if item then
                        -- Fond coloré selon le type d'objet
                        local itemColor = Color(80, 80, 80, 150)
                        if item.category == "food" then itemColor = Color(100, 150, 100, 150)
                        elseif item.category == "medical" then itemColor = Color(150, 100, 100, 150)
                        elseif item.category == "weapons" then itemColor = Color(150, 150, 100, 150)
                        elseif item.category == "ammo" then itemColor = Color(120, 120, 150, 150)
                        end

                        surface.SetDrawColor(itemColor)
                        surface.DrawRect(3, 3, w-6, h-6)

                        -- Nom de l'objet
                        local shortName = string.sub(item.name, 1, 6)
                        if string.len(item.name) > 6 then
                            shortName = shortName .. ".."
                        end

                        draw.SimpleText(shortName, "DermaDefault", w/2, 8, TEXT_HIGHLIGHT, TEXT_ALIGN_CENTER, TEXT_ALIGN_TOP)

                        -- Quantité avec style DayZ
                        if slotData.quantity > 1 then
                            surface.SetDrawColor(ACCENT_COLOR)
                            surface.DrawRect(w-18, h-15, 16, 12)
                            draw.SimpleText(slotData.quantity, "DermaDefaultBold", w-10, h-9, Color(0, 0, 0, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
                        end

                        -- Indicateur de consommable
                        if item.consumable then
                            surface.SetDrawColor(ACCENT_COLOR)
                            surface.DrawRect(3, 3, 8, 8)
                            draw.SimpleText("C", "DermaDefault", 7, 7, Color(0, 0, 0, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
                        end
                    end
                end
            
                -- Tooltip style DayZ
                if item then
                    slot:SetTooltip(item.name .. "\n" .. (item.description or "") .. "\n\nClic droit pour utiliser")
                end

                -- Clic droit pour utiliser
                slot.OnMousePressed = function(self, keyCode)
                    if keyCode == MOUSE_RIGHT and item and item.consumable then
                        -- Envoyer la demande d'utilisation au serveur
                        net.Start("ApocalypseSurvival_UseItem")
                        net.WriteString(category.id)
                        net.WriteInt(i, 8)
                        net.SendToServer()

                        -- Fermer l'inventaire après utilisation
                        timer.Simple(0.1, function()
                            if IsValid(ApocalypseSurvival.InventoryPanel) then
                                ApocalypseSurvival.CloseInventory()
                            end
                        end)
                    end
                end
            end

            -- Calculer la hauteur nécessaire pour cette catégorie
            local rows = math.ceil(category.slots / slotsPerRow)
            yPos = yPos + rows * (slotSize + slotSpacing) + 25
        end

        return yPos
    end

    -- Créer les sections
    CreateCategorySection(leftPanel, leftCategories, 35)
    CreateCategorySection(rightPanel, rightCategories, 35)
    
    -- Panel d'instructions style DayZ
    local instructionsPanel = vgui.Create("DPanel", content)
    instructionsPanel:SetPos(10, panelH - 90)
    instructionsPanel:SetSize(panelW - 40, 70)
    instructionsPanel.Paint = function(self, w, h)
        surface.SetDrawColor(CATEGORY_HEADER)
        surface.DrawRect(0, 0, w, h)
        surface.SetDrawColor(ACCENT_COLOR)
        surface.DrawOutlinedRect(0, 0, w, h)

        -- Titre
        draw.SimpleText("CONTRÔLES", "DermaDefaultBold", 10, 5, TEXT_HIGHLIGHT, TEXT_ALIGN_LEFT, TEXT_ALIGN_TOP)

        -- Instructions
        local instructions = {
            "• Clic droit sur un objet consommable pour l'utiliser",
            "• [C] = Objet consommable  •  Couleurs = Types d'objets",
            "• Fermez avec [I], [Échap] ou le bouton [✕]"
        }

        for i, instruction in ipairs(instructions) do
            draw.SimpleText(instruction, "DermaDefault", 15, 15 + (i * 15), TEXT_COLOR, TEXT_ALIGN_LEFT, TEXT_ALIGN_TOP)
        end
    end

    -- Bouton de fermeture alternatif en bas
    local closeBtn2 = vgui.Create("DButton", content)
    closeBtn2:SetPos(panelW - 120, panelH - 90)
    closeBtn2:SetSize(100, 30)
    closeBtn2:SetText("FERMER")
    closeBtn2:SetFont("DermaDefaultBold")
    closeBtn2:SetTextColor(Color(255, 255, 255, 255))
    closeBtn2:SetCursor("hand")

    closeBtn2.Paint = function(self, w, h)
        local isHovered = self:IsHovered()
        local color = isHovered and Color(200, 80, 80, 220) or Color(80, 80, 80, 200)

        surface.SetDrawColor(color)
        surface.DrawRect(0, 0, w, h)
        surface.SetDrawColor(ACCENT_COLOR)
        surface.DrawOutlinedRect(0, 0, w, h, 2)

        if isHovered then
            surface.SetDrawColor(Color(255, 255, 255, 30))
            surface.DrawRect(2, 2, w-4, h-4)
        end
    end

    closeBtn2.DoClick = function()
        print("[Apocalypse Survival] Bouton FERMER cliqué")
        ApocalypseSurvival.CloseInventory()
    end

    -- Test de cliquabilité
    closeBtn2.OnMousePressed = function(self, keyCode)
        print("[Apocalypse Survival] Mouse pressed sur bouton FERMER, keyCode:", keyCode)
        if keyCode == MOUSE_LEFT then
            ApocalypseSurvival.CloseInventory()
        end
    end
end

-- Ouvrir l'inventaire
function ApocalypseSurvival.OpenInventory()
    print("[Apocalypse Survival] Tentative d'ouverture de l'inventaire")

    if ApocalypseSurvival.InventoryOpen then
        print("[Apocalypse Survival] Inventaire déjà ouvert")
        return
    end

    print("[Apocalypse Survival] Ouverture de l'inventaire...")
    ApocalypseSurvival.InventoryOpen = true
    ApocalypseSurvival.CreateInventoryPanel()
    gui.EnableScreenClicker(true)
    print("[Apocalypse Survival] Inventaire ouvert avec succès")
end

-- Fermer l'inventaire
function ApocalypseSurvival.CloseInventory()
    print("[Apocalypse Survival] Tentative de fermeture de l'inventaire")

    if not ApocalypseSurvival.InventoryOpen then
        print("[Apocalypse Survival] Inventaire déjà fermé")
        return
    end

    print("[Apocalypse Survival] Fermeture de l'inventaire...")
    ApocalypseSurvival.InventoryOpen = false

    if IsValid(ApocalypseSurvival.InventoryPanel) then
        ApocalypseSurvival.InventoryPanel:Remove()
        ApocalypseSurvival.InventoryPanel = nil
    end

    gui.EnableScreenClicker(false)
    print("[Apocalypse Survival] Inventaire fermé avec succès")
end

-- Toggle inventaire
function ApocalypseSurvival.ToggleInventory()
    print("[Apocalypse Survival] Toggle inventaire appelé")
    if ApocalypseSurvival.InventoryOpen then
        print("[Apocalypse Survival] Fermeture de l'inventaire")
        ApocalypseSurvival.CloseInventory()
    else
        print("[Apocalypse Survival] Ouverture de l'inventaire")
        ApocalypseSurvival.OpenInventory()
    end
end

-- Gestion simplifiée des touches
ApocalypseSurvival.KeyPressed = false

hook.Add("Think", "ApocalypseSurvival_InventoryKey", function()
    local ply = LocalPlayer()
    if not IsValid(ply) then return end

    -- Vérifier si la touche I est pressée
    if input.IsKeyDown(KEY_I) then
        if not ApocalypseSurvival.KeyPressed then
            ApocalypseSurvival.KeyPressed = true
            print("[Apocalypse Survival] Touche I détectée")
            ApocalypseSurvival.ToggleInventory()
        end
    else
        ApocalypseSurvival.KeyPressed = false
    end
end)

-- Fermer avec Échap
hook.Add("PlayerBindPress", "ApocalypseSurvival_CloseInventory", function(ply, bind, pressed)
    if ply == LocalPlayer() and pressed and ApocalypseSurvival.InventoryOpen then
        if bind == "cancelselect" or bind == "+menu" or bind == "+menu_context" then
            ApocalypseSurvival.CloseInventory()
            return true
        end
    end
end)

-- Gestion globale des touches de fermeture
hook.Add("Think", "ApocalypseSurvival_CloseKeys", function()
    if not ApocalypseSurvival.InventoryOpen then return end

    -- Échap pour fermer
    if input.IsKeyDown(KEY_ESCAPE) then
        if not ApocalypseSurvival.EscapePressed then
            ApocalypseSurvival.EscapePressed = true
            print("[Apocalypse Survival] Touche Échap détectée")
            ApocalypseSurvival.CloseInventory()
        end
    else
        ApocalypseSurvival.EscapePressed = false
    end
end)

-- Mettre à jour l'inventaire quand les données changent
local oldUpdateData = net.Receive
hook.Add("Think", "ApocalypseSurvival_UpdateInventory", function()
    if ApocalypseSurvival.InventoryOpen and IsValid(ApocalypseSurvival.InventoryPanel) then
        -- Recréer le panel si les données ont changé
        -- (Pour une version plus optimisée, on pourrait juste mettre à jour les slots modifiés)
        timer.Simple(0.1, function()
            if ApocalypseSurvival.InventoryOpen then
                ApocalypseSurvival.CreateInventoryPanel()
            end
        end)
    end
end)

-- Commandes console
concommand.Add("apocalypse_inventory", function()
    ApocalypseSurvival.ToggleInventory()
end)

concommand.Add("apocalypse_inventory_open", function()
    ApocalypseSurvival.OpenInventory()
end)

concommand.Add("apocalypse_inventory_close", function()
    ApocalypseSurvival.CloseInventory()
end)

-- Commande alternative
concommand.Add("+apocalypse_inventory", function()
    ApocalypseSurvival.OpenInventory()
end)

concommand.Add("-apocalypse_inventory", function()
    -- Ne rien faire sur le relâchement
end)

-- Bind automatique de la touche I
hook.Add("InitPostEntity", "ApocalypseSurvival_BindKey", function()
    timer.Simple(1, function()
        LocalPlayer():ConCommand("bind i apocalypse_inventory")
        print("[Apocalypse Survival] Touche I liée à l'inventaire")
    end)
end)

-- Commande de test pour vérifier l'état de l'inventaire
concommand.Add("apocalypse_debug", function()
    print("=== DEBUG APOCALYPSE SURVIVAL ===")
    print("InventoryOpen:", ApocalypseSurvival.InventoryOpen)
    print("InventoryPanel valid:", IsValid(ApocalypseSurvival.InventoryPanel))
    print("Screen clicker enabled:", gui.IsGameUIVisible())
    print("LocalPlayer valid:", IsValid(LocalPlayer()))

    if IsValid(ApocalypseSurvival.InventoryPanel) then
        local frame = ApocalypseSurvival.InventoryPanel
        print("Frame size:", frame:GetWide(), "x", frame:GetTall())
        print("Frame pos:", frame:GetPos())
        print("Frame visible:", frame:IsVisible())
    end
    print("================================")
end)
