-- Interface d'inventaire pour le système de survie apocalypse - Côté client
ApocalypseSurvival = ApocalypseSurvival or {}

-- Vérifier que la configuration et les objets sont chargés
if not ApocalypseSurvival.Config then
    include("autorun/shared/sh_apocalypse_config.lua")
end
if not ApocalypseSurvival.Items then
    include("autorun/shared/sh_apocalypse_items.lua")
end

ApocalypseSurvival.InventoryOpen = false
ApocalypseSurvival.InventoryPanel = nil

-- Couleurs pour l'interface
local SLOT_COLOR = Color(60, 60, 60, 200)
local SLOT_HOVER_COLOR = Color(80, 80, 80, 220)
local SLOT_BORDER_COLOR = Color(100, 100, 100, 255)
local CATEGORY_COLOR = Color(40, 40, 40, 240)
local TEXT_COLOR = Color(255, 255, 255, 255)

-- Fonction pour créer l'interface d'inventaire
function ApocalypseSurvival.CreateInventoryPanel()
    -- Vérifier que tout est chargé
    if not ApocalypseSurvival.Config or not ApocalypseSurvival.ClientData then
        return
    end

    if IsValid(ApocalypseSurvival.InventoryPanel) then
        ApocalypseSurvival.InventoryPanel:Remove()
    end
    
    local scrW, scrH = ScrW(), ScrH()
    local panelW, panelH = 800, 600
    
    -- Panel principal
    local frame = vgui.Create("DFrame")
    frame:SetSize(panelW, panelH)
    frame:SetPos((scrW - panelW) / 2, (scrH - panelH) / 2)
    frame:SetTitle("Inventaire de Survie")
    frame:SetDraggable(true)
    frame:SetDeleteOnClose(false)
    frame:MakePopup()
    
    ApocalypseSurvival.InventoryPanel = frame
    
    -- Fonction de fermeture
    frame.OnClose = function()
        ApocalypseSurvival.InventoryOpen = false
        gui.EnableScreenClicker(false)
    end
    
    -- Panel de contenu
    local content = vgui.Create("DPanel", frame)
    content:Dock(FILL)
    content:DockMargin(5, 5, 5, 5)
    content.Paint = function(self, w, h)
        surface.SetDrawColor(30, 30, 30, 240)
        surface.DrawRect(0, 0, w, h)
    end
    
    -- Créer les catégories
    local categories = {
        {id = "weapons", name = "Armes", slots = ApocalypseSurvival.Config.InventorySlots.weapons},
        {id = "ammo", name = "Munitions", slots = ApocalypseSurvival.Config.InventorySlots.ammo},
        {id = "food", name = "Nourriture", slots = ApocalypseSurvival.Config.InventorySlots.food},
        {id = "medical", name = "Médical", slots = ApocalypseSurvival.Config.InventorySlots.medical},
        {id = "misc", name = "Divers", slots = ApocalypseSurvival.Config.InventorySlots.misc}
    }
    
    local yPos = 10
    
    for _, category in ipairs(categories) do
        -- Titre de catégorie
        local categoryLabel = vgui.Create("DLabel", content)
        categoryLabel:SetPos(10, yPos)
        categoryLabel:SetSize(200, 25)
        categoryLabel:SetText(category.name .. " (" .. #(ApocalypseSurvival.ClientData.inventory[category.id] or {}) .. "/" .. category.slots .. ")")
        categoryLabel:SetFont("DermaDefaultBold")
        categoryLabel:SetTextColor(TEXT_COLOR)
        
        yPos = yPos + 30
        
        -- Grille de slots
        local slotsPerRow = 8
        local slotSize = 60
        local slotSpacing = 5
        
        for i = 1, category.slots do
            local row = math.floor((i - 1) / slotsPerRow)
            local col = (i - 1) % slotsPerRow
            
            local slotX = 10 + col * (slotSize + slotSpacing)
            local slotY = yPos + row * (slotSize + slotSpacing)
            
            local slot = vgui.Create("DPanel", content)
            slot:SetPos(slotX, slotY)
            slot:SetSize(slotSize, slotSize)
            slot:SetCursor("hand")
            
            -- Données du slot
            local slotData = (ApocalypseSurvival.ClientData.inventory[category.id] or {})[i]
            local item = slotData and ApocalypseSurvival.GetItem(slotData.id)
            
            slot.Paint = function(self, w, h)
                local color = self:IsHovered() and SLOT_HOVER_COLOR or SLOT_COLOR
                surface.SetDrawColor(color)
                surface.DrawRect(0, 0, w, h)
                
                surface.SetDrawColor(SLOT_BORDER_COLOR)
                surface.DrawOutlinedRect(0, 0, w, h)
                
                -- Dessiner l'objet s'il existe
                if item then
                    -- Nom de l'objet (raccourci)
                    local shortName = string.sub(item.name, 1, 8)
                    if string.len(item.name) > 8 then
                        shortName = shortName .. "..."
                    end
                    
                    draw.SimpleText(shortName, "DermaDefault", w/2, 10, TEXT_COLOR, TEXT_ALIGN_CENTER, TEXT_ALIGN_TOP)
                    
                    -- Quantité
                    if slotData.quantity > 1 then
                        draw.SimpleText("x" .. slotData.quantity, "DermaDefaultBold", w - 5, h - 5, Color(255, 255, 0, 255), TEXT_ALIGN_RIGHT, TEXT_ALIGN_BOTTOM)
                    end
                    
                    -- Indicateur de consommable
                    if item.consumable then
                        surface.SetDrawColor(0, 255, 0, 100)
                        surface.DrawRect(2, 2, 8, 8)
                    end
                end
            end
            
            -- Tooltip
            if item then
                slot:SetTooltip(item.name .. "\n" .. (item.description or ""))
            end
            
            -- Clic droit pour utiliser
            slot.OnMousePressed = function(self, keyCode)
                if keyCode == MOUSE_RIGHT and item and item.consumable then
                    -- Envoyer la demande d'utilisation au serveur
                    net.Start("ApocalypseSurvival_UseItem")
                    net.WriteString(category.id)
                    net.WriteInt(i, 8)
                    net.SendToServer()
                    
                    -- Fermer l'inventaire après utilisation
                    timer.Simple(0.1, function()
                        if IsValid(ApocalypseSurvival.InventoryPanel) then
                            ApocalypseSurvival.CloseInventory()
                        end
                    end)
                end
            end
        end
        
        -- Calculer la hauteur nécessaire pour cette catégorie
        local rows = math.ceil(category.slots / slotsPerRow)
        yPos = yPos + rows * (slotSize + slotSpacing) + 20
    end
    
    -- Instructions en bas
    local instructions = vgui.Create("DLabel", content)
    instructions:SetPos(10, panelH - 80)
    instructions:SetSize(panelW - 20, 60)
    instructions:SetText("Clic droit sur un objet consommable pour l'utiliser\nLes objets verts peuvent être consommés\nFermez avec [I] ou [Échap]")
    instructions:SetFont("DermaDefault")
    instructions:SetTextColor(Color(200, 200, 200, 255))
    instructions:SetContentAlignment(7) -- Alignement en bas à gauche
end

-- Ouvrir l'inventaire
function ApocalypseSurvival.OpenInventory()
    print("[Apocalypse Survival] Tentative d'ouverture de l'inventaire")

    if ApocalypseSurvival.InventoryOpen then
        print("[Apocalypse Survival] Inventaire déjà ouvert")
        return
    end

    print("[Apocalypse Survival] Ouverture de l'inventaire...")
    ApocalypseSurvival.InventoryOpen = true
    ApocalypseSurvival.CreateInventoryPanel()
    gui.EnableScreenClicker(true)
    print("[Apocalypse Survival] Inventaire ouvert avec succès")
end

-- Fermer l'inventaire
function ApocalypseSurvival.CloseInventory()
    if not ApocalypseSurvival.InventoryOpen then return end
    
    ApocalypseSurvival.InventoryOpen = false
    if IsValid(ApocalypseSurvival.InventoryPanel) then
        ApocalypseSurvival.InventoryPanel:Close()
    end
    gui.EnableScreenClicker(false)
end

-- Toggle inventaire
function ApocalypseSurvival.ToggleInventory()
    print("[Apocalypse Survival] Toggle inventaire appelé")
    if ApocalypseSurvival.InventoryOpen then
        print("[Apocalypse Survival] Fermeture de l'inventaire")
        ApocalypseSurvival.CloseInventory()
    else
        print("[Apocalypse Survival] Ouverture de l'inventaire")
        ApocalypseSurvival.OpenInventory()
    end
end

-- Gestion des touches avec input.IsKeyDown
hook.Add("Think", "ApocalypseSurvival_InventoryKey", function()
    local ply = LocalPlayer()
    if not IsValid(ply) then return end

    -- Vérifier si la touche I est pressée
    if input.IsKeyDown(KEY_I) then
        if not ApocalypseSurvival.KeyPressed then
            ApocalypseSurvival.KeyPressed = true
            ApocalypseSurvival.ToggleInventory()
        end
    else
        ApocalypseSurvival.KeyPressed = false
    end
end)

-- Fermer avec Échap
hook.Add("OnPlayerChat", "ApocalypseSurvival_CloseInventory", function(ply, text)
    if ply == LocalPlayer() and text == "cancelselect" and ApocalypseSurvival.InventoryOpen then
        ApocalypseSurvival.CloseInventory()
        return true
    end
end)

-- Mettre à jour l'inventaire quand les données changent
local oldUpdateData = net.Receive
hook.Add("Think", "ApocalypseSurvival_UpdateInventory", function()
    if ApocalypseSurvival.InventoryOpen and IsValid(ApocalypseSurvival.InventoryPanel) then
        -- Recréer le panel si les données ont changé
        -- (Pour une version plus optimisée, on pourrait juste mettre à jour les slots modifiés)
        timer.Simple(0.1, function()
            if ApocalypseSurvival.InventoryOpen then
                ApocalypseSurvival.CreateInventoryPanel()
            end
        end)
    end
end)

-- Commande console pour ouvrir l'inventaire
concommand.Add("apocalypse_inventory", function()
    ApocalypseSurvival.ToggleInventory()
end)

-- Commande alternative
concommand.Add("+apocalypse_inventory", function()
    ApocalypseSurvival.OpenInventory()
end)

concommand.Add("-apocalypse_inventory", function()
    -- Ne rien faire sur le relâchement
end)

-- Bind automatique de la touche I
hook.Add("InitPostEntity", "ApocalypseSurvival_BindKey", function()
    timer.Simple(1, function()
        LocalPlayer():ConCommand("bind i apocalypse_inventory")
        print("[Apocalypse Survival] Touche I liée à l'inventaire")
    end)
end)
