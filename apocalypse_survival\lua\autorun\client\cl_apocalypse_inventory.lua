-- Interface d'inventaire pour le système de survie apocalypse - Côté client
ApocalypseSurvival = ApocalypseSurvival or {}

-- Vérifier que la configuration et les objets sont chargés
if not ApocalypseSurvival.Config then
    include("autorun/shared/sh_apocalypse_config.lua")
end
if not ApocalypseSurvival.Items then
    include("autorun/shared/sh_apocalypse_items.lua")
end

ApocalypseSurvival.InventoryOpen = false
ApocalypseSurvival.InventoryPanel = nil
ApocalypseSurvival.WeaponsDisabled = false

-- Couleurs pour l'interface style DayZ
local SLOT_COLOR = Color(45, 45, 45, 230)
local SLOT_HOVER_COLOR = Color(70, 70, 70, 250)
local SLOT_BORDER_COLOR = Color(120, 120, 120, 255)
local SLOT_BORDER_HOVER = Color(200, 200, 200, 255)
local CATEGORY_COLOR = Color(25, 25, 25, 250)
local CATEGORY_HEADER = Color(35, 35, 35, 255)
local TEXT_COLOR = Color(220, 220, 220, 255)
local TEXT_HIGHLIGHT = Color(255, 255, 255, 255)
local BACKGROUND_COLOR = Color(20, 20, 20, 240)
local ACCENT_COLOR = Color(180, 140, 80, 255) -- Couleur dorée/beige style militaire

-- Fonctions pour désactiver/réactiver les armes et actions
function ApocalypseSurvival.DisableWeapons()
    if ApocalypseSurvival.WeaponsDisabled then return end

    ApocalypseSurvival.WeaponsDisabled = true
    print("[Apocalypse Survival] Armes et actions désactivées")

    -- Empêcher les attaques et actions
    hook.Add("PlayerBindPress", "ApocalypseSurvival_BlockActions", function(ply, bind, pressed)
        if ply ~= LocalPlayer() or not ApocalypseSurvival.InventoryOpen then return end

        -- Bloquer les actions d'armes et de mouvement
        local blockedBinds = {
            "+attack", "+attack2", "+reload", "+use", "+zoom",
            "+speed", "+walk", "+jump", "+duck", "+moveup", "+movedown",
            "impulse 100", "impulse 101", "lastinv", "invnext", "invprev",
            "+voicerecord", "+showscores", "+menu", "+menu_context"
        }

        for _, blockedBind in ipairs(blockedBinds) do
            if bind == blockedBind then
                return true -- Bloquer l'action
            end
        end
    end)

    -- Empêcher le changement d'arme
    hook.Add("PlayerSwitchWeapon", "ApocalypseSurvival_BlockWeaponSwitch", function(ply, oldWeapon, newWeapon)
        if ply == LocalPlayer() and ApocalypseSurvival.InventoryOpen then
            return true -- Empêcher le changement
        end
    end)

    -- Bloquer les inputs de souris pour les armes
    hook.Add("InputMouseApply", "ApocalypseSurvival_BlockMouse", function(cmd, x, y, angle)
        if ApocalypseSurvival.InventoryOpen then
            -- Permettre le mouvement de souris pour l'interface mais pas pour la vue
            cmd:SetMouseX(0)
            cmd:SetMouseY(0)
            return true
        end
    end)

    -- Bloquer les commandes d'armes
    hook.Add("OnPlayerChat", "ApocalypseSurvival_BlockCommands", function(ply, text, team, dead)
        if ply == LocalPlayer() and ApocalypseSurvival.InventoryOpen then
            -- Bloquer les commandes d'armes communes
            local blockedCommands = {
                "use weapon_", "give weapon_", "impulse", "lastinv", "invnext", "invprev"
            }

            for _, cmd in ipairs(blockedCommands) do
                if string.find(text, cmd) then
                    return true -- Bloquer la commande
                end
            end
        end
    end)

    -- Empêcher les actions de CreateMove
    hook.Add("CreateMove", "ApocalypseSurvival_BlockMove", function(cmd)
        if ApocalypseSurvival.InventoryOpen then
            -- Bloquer les attaques et actions
            cmd:RemoveKey(IN_ATTACK)
            cmd:RemoveKey(IN_ATTACK2)
            cmd:RemoveKey(IN_RELOAD)
            cmd:RemoveKey(IN_USE)
            cmd:RemoveKey(IN_ZOOM)
            cmd:RemoveKey(IN_SPEED)
            cmd:RemoveKey(IN_WALK)
            cmd:RemoveKey(IN_JUMP)
            cmd:RemoveKey(IN_DUCK)

            -- Empêcher le mouvement (optionnel - décommentez si vous voulez bloquer le mouvement)
            -- cmd:SetForwardMove(0)
            -- cmd:SetSideMove(0)
            -- cmd:SetUpMove(0)
        end
    end)
end

function ApocalypseSurvival.EnableWeapons()
    if not ApocalypseSurvival.WeaponsDisabled then return end

    ApocalypseSurvival.WeaponsDisabled = false
    print("[Apocalypse Survival] Armes et actions réactivées")

    -- Supprimer tous les hooks de blocage
    hook.Remove("PlayerBindPress", "ApocalypseSurvival_BlockActions")
    hook.Remove("PlayerSwitchWeapon", "ApocalypseSurvival_BlockWeaponSwitch")
    hook.Remove("InputMouseApply", "ApocalypseSurvival_BlockMouse")
    hook.Remove("OnPlayerChat", "ApocalypseSurvival_BlockCommands")
    hook.Remove("CreateMove", "ApocalypseSurvival_BlockMove")
end

-- Fonction pour créer l'interface d'inventaire
function ApocalypseSurvival.CreateInventoryPanel()
    -- Vérifier que tout est chargé
    if not ApocalypseSurvival.Config or not ApocalypseSurvival.ClientData then
        return
    end

    if IsValid(ApocalypseSurvival.InventoryPanel) then
        ApocalypseSurvival.InventoryPanel:Remove()
    end
    
    local scrW, scrH = ScrW(), ScrH()
    local panelW, panelH = 1000, 700

    -- Panel principal style DayZ
    local frame = vgui.Create("DFrame")
    frame:SetSize(panelW, panelH)
    frame:SetPos((scrW - panelW) / 2, (scrH - panelH) / 2)
    frame:SetTitle("")
    frame:SetDraggable(true)
    frame:SetDeleteOnClose(false)
    frame:ShowCloseButton(false)
    frame:MakePopup()

    -- Style DayZ pour le frame
    frame.Paint = function(self, w, h)
        -- Fond principal
        surface.SetDrawColor(BACKGROUND_COLOR)
        surface.DrawRect(0, 0, w, h)

        -- Bordure extérieure
        surface.SetDrawColor(ACCENT_COLOR)
        surface.DrawOutlinedRect(0, 0, w, h, 2)

        -- Barre de titre style militaire
        surface.SetDrawColor(CATEGORY_HEADER)
        surface.DrawRect(0, 0, w, 35)

        surface.SetDrawColor(ACCENT_COLOR)
        surface.DrawOutlinedRect(0, 0, w, 35)

        -- Titre
        draw.SimpleText("INVENTAIRE DE SURVIE", "DermaLarge", w/2, 17, TEXT_HIGHLIGHT, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)

        -- Lignes décoratives
        surface.SetDrawColor(ACCENT_COLOR.r, ACCENT_COLOR.g, ACCENT_COLOR.b, 100)
        surface.DrawRect(10, 40, w-20, 1)
        surface.DrawRect(10, h-10, w-20, 1)
    end
    
    ApocalypseSurvival.InventoryPanel = frame
    
    -- Fonction de fermeture
    frame.OnClose = function()
        print("[Apocalypse Survival] Frame OnClose appelé")
        ApocalypseSurvival.InventoryOpen = false
        gui.EnableScreenClicker(false)
    end

    -- Empêcher la fermeture automatique par défaut
    frame.Think = function(self)
        if input.IsKeyDown(KEY_ESCAPE) and not self.EscapePressed then
            self.EscapePressed = true
            ApocalypseSurvival.CloseInventory()
        elseif not input.IsKeyDown(KEY_ESCAPE) then
            self.EscapePressed = false
        end
    end
    
    -- Bouton de fermeture style DayZ (plus grand et plus visible)
    local closeBtn = vgui.Create("DButton", frame)
    closeBtn:SetSize(40, 30)
    closeBtn:SetPos(panelW - 45, 3)
    closeBtn:SetText("")
    closeBtn:SetFont("DermaLarge")
    closeBtn:SetTextColor(Color(255, 255, 255, 255))
    closeBtn:SetCursor("hand")

    closeBtn.Paint = function(self, w, h)
        local isHovered = self:IsHovered()
        local color = isHovered and Color(200, 80, 80, 220) or Color(100, 100, 100, 200)
        local textColor = isHovered and Color(255, 255, 255, 255) or Color(220, 220, 220, 255)

        -- Fond du bouton
        surface.SetDrawColor(color)
        surface.DrawRect(0, 0, w, h)

        -- Bordure
        surface.SetDrawColor(ACCENT_COLOR)
        surface.DrawOutlinedRect(0, 0, w, h, 2)

        -- Effet hover
        if isHovered then
            surface.SetDrawColor(Color(255, 255, 255, 50))
            surface.DrawRect(2, 2, w-4, h-4)
        end

        -- Texte X centré
        draw.SimpleText("✕", "DermaLarge", w/2, h/2, textColor, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end

    closeBtn.DoClick = function(self)
        print("[Apocalypse Survival] Bouton de fermeture cliqué")
        ApocalypseSurvival.CloseInventory()
    end

    closeBtn.OnMousePressed = function(self, keyCode)
        if keyCode == MOUSE_LEFT then
            print("[Apocalypse Survival] Clic gauche sur bouton de fermeture")
            ApocalypseSurvival.CloseInventory()
        end
    end

    -- Panel de contenu style DayZ
    local content = vgui.Create("DPanel", frame)
    content:SetPos(10, 50)
    content:SetSize(panelW - 20, panelH - 70)
    content.Paint = function(self, w, h)
        -- Fond légèrement transparent
        surface.SetDrawColor(CATEGORY_COLOR)
        surface.DrawRect(0, 0, w, h)

        -- Bordure interne
        surface.SetDrawColor(ACCENT_COLOR.r, ACCENT_COLOR.g, ACCENT_COLOR.b, 150)
        surface.DrawOutlinedRect(0, 0, w, h)
    end
    
    -- Inventaire unifié style DayZ - une seule grille
    local inventoryPanel = vgui.Create("DPanel", content)
    inventoryPanel:SetPos(10, 10)
    inventoryPanel:SetSize(panelW - 40, panelH - 100)
    inventoryPanel.Paint = function(self, w, h)
        surface.SetDrawColor(CATEGORY_HEADER)
        surface.DrawRect(0, 0, w, 35)
        surface.SetDrawColor(ACCENT_COLOR)
        surface.DrawOutlinedRect(0, 0, w, h)

        -- Titre avec compteur
        local used = #(ApocalypseSurvival.ClientData.inventory.unified or {})
        local maxSlots = ApocalypseSurvival.Config.InventorySlots.unified
        local title = "🎒 INVENTAIRE UNIFIÉ [" .. used .. "/" .. maxSlots .. "]"
        draw.SimpleText(title, "DermaLarge", w/2, 17, TEXT_HIGHLIGHT, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)

        -- Barre de remplissage
        local fillPercent = used / maxSlots
        local fillColor = fillPercent >= 1 and Color(200, 100, 100, 200) or
                         fillPercent >= 0.8 and Color(200, 200, 100, 200) or
                         Color(100, 200, 100, 200)

        surface.SetDrawColor(Color(40, 40, 40, 200))
        surface.DrawRect(10, h - 15, w - 20, 8)
        surface.SetDrawColor(fillColor)
        surface.DrawRect(10, h - 15, (w - 20) * fillPercent, 8)
        surface.SetDrawColor(ACCENT_COLOR)
        surface.DrawOutlinedRect(10, h - 15, w - 20, 8)
    end
    
    -- Créer la grille d'inventaire unifié
    local slotsPerRow = 10
    local slotSize = 60
    local slotSpacing = 5
    local startX = 20
    local startY = 50
    local maxSlots = ApocalypseSurvival.Config.InventorySlots.unified

    for i = 1, maxSlots do
        local row = math.floor((i - 1) / slotsPerRow)
        local col = (i - 1) % slotsPerRow

        local slotX = startX + col * (slotSize + slotSpacing)
        local slotY = startY + row * (slotSize + slotSpacing)

        local slot = vgui.Create("DPanel", inventoryPanel)
        slot:SetPos(slotX, slotY)
        slot:SetSize(slotSize, slotSize)
        slot:SetCursor("hand")

        -- Données du slot
        local slotData = (ApocalypseSurvival.ClientData.inventory.unified or {})[i]
        local item = slotData and ApocalypseSurvival.GetItem(slotData.id)

        -- Style DayZ pour les slots
        slot.Paint = function(self, w, h)
            local isHovered = self:IsHovered()
            local borderColor = isHovered and SLOT_BORDER_HOVER or SLOT_BORDER_COLOR
            local bgColor = isHovered and SLOT_HOVER_COLOR or SLOT_COLOR

            -- Fond du slot avec effet de profondeur
            surface.SetDrawColor(Color(bgColor.r - 10, bgColor.g - 10, bgColor.b - 10, bgColor.a))
            surface.DrawRect(2, 2, w-2, h-2)
            surface.SetDrawColor(bgColor)
            surface.DrawRect(0, 0, w-2, h-2)

            -- Bordure avec effet 3D
            surface.SetDrawColor(borderColor)
            surface.DrawOutlinedRect(0, 0, w, h, 2)

            -- Grille interne style DayZ
            if not item then
                surface.SetDrawColor(borderColor.r, borderColor.g, borderColor.b, 50)
                for x = 10, w-10, 10 do
                    surface.DrawLine(x, 5, x, h-5)
                end
                for y = 10, h-10, 10 do
                    surface.DrawLine(5, y, w-5, y)
                end
            end

            -- Dessiner l'objet s'il existe
            if item then
                -- Fond coloré selon le type d'objet
                local itemColor = Color(80, 80, 80, 150)
                if item.category == "food" then itemColor = Color(100, 150, 100, 150)
                elseif item.category == "medical" then itemColor = Color(150, 100, 100, 150)
                elseif item.category == "weapons" then itemColor = Color(150, 150, 100, 150)
                elseif item.category == "ammo" then itemColor = Color(120, 120, 150, 150)
                end

                surface.SetDrawColor(itemColor)
                surface.DrawRect(3, 3, w-6, h-6)

                -- Nom de l'objet
                local shortName = string.sub(item.name, 1, 6)
                if string.len(item.name) > 6 then
                    shortName = shortName .. ".."
                end

                draw.SimpleText(shortName, "DermaDefault", w/2, 8, TEXT_HIGHLIGHT, TEXT_ALIGN_CENTER, TEXT_ALIGN_TOP)

                -- Quantité avec style DayZ
                if slotData.quantity > 1 then
                    surface.SetDrawColor(ACCENT_COLOR)
                    surface.DrawRect(w-18, h-15, 16, 12)
                    draw.SimpleText(slotData.quantity, "DermaDefaultBold", w-10, h-9, Color(0, 0, 0, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
                end

                -- Indicateur de type d'objet
                local typeIcon = "?"
                if item.category == "weapons" then typeIcon = "W"
                elseif item.category == "food" then typeIcon = "F"
                elseif item.category == "medical" then typeIcon = "M"
                elseif item.category == "ammo" then typeIcon = "A"
                elseif item.category == "misc" then typeIcon = "T"
                end

                surface.SetDrawColor(ACCENT_COLOR)
                surface.DrawRect(3, 3, 12, 12)
                draw.SimpleText(typeIcon, "DermaDefault", 9, 9, Color(0, 0, 0, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
            end
        end

        -- Tooltip style DayZ
        if item then
            slot:SetTooltip(item.name .. "\n" .. (item.description or "") ..
                           "\n\nClic droit pour " .. (item.weapon_class and "équiper" or "utiliser"))
        end

        -- Clic droit pour utiliser/équiper
        slot.OnMousePressed = function(self, keyCode)
            if keyCode == MOUSE_RIGHT and item then
                -- Envoyer la demande d'utilisation au serveur
                net.Start("ApocalypseSurvival_UseItem")
                net.WriteInt(i, 8)
                net.SendToServer()

                -- Fermer l'inventaire après utilisation si c'est un consommable
                if item.consumable then
                    timer.Simple(0.1, function()
                        if IsValid(ApocalypseSurvival.InventoryPanel) then
                            ApocalypseSurvival.CloseInventory()
                        end
                    end)
                end
            end
        end
    end

    -- Slots d'équipement pour les armes (en bas de l'inventaire)
    local weaponSlotsPanel = vgui.Create("DPanel", content)
    weaponSlotsPanel:SetPos(10, panelH - 150)
    weaponSlotsPanel:SetSize(panelW - 40, 80)
    weaponSlotsPanel.Paint = function(self, w, h)
        surface.SetDrawColor(CATEGORY_HEADER)
        surface.DrawRect(0, 0, w, 25)
        surface.SetDrawColor(ACCENT_COLOR)
        surface.DrawOutlinedRect(0, 0, w, h)

        -- Titre
        draw.SimpleText("🔫 ARMES ÉQUIPÉES", "DermaDefaultBold", w/2, 12, TEXT_HIGHLIGHT, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end

    -- Créer les 2 slots d'armes
    local weaponSlotSize = 60
    local weaponSlotSpacing = 20
    local startX = (weaponSlotsPanel:GetWide() - (2 * weaponSlotSize + weaponSlotSpacing)) / 2

    for i = 1, 2 do
        local slotX = startX + (i - 1) * (weaponSlotSize + weaponSlotSpacing)
        local slotY = 30

        local weaponSlot = vgui.Create("DPanel", weaponSlotsPanel)
        weaponSlot:SetPos(slotX, slotY)
        weaponSlot:SetSize(weaponSlotSize, weaponSlotSize)
        weaponSlot:SetCursor("hand")

        weaponSlot.Paint = function(self, w, h)
            -- Obtenir l'arme équipée pour ce slot depuis les données du serveur (en temps réel)
            local weaponData = nil
            if ApocalypseSurvival.ClientData and ApocalypseSurvival.ClientData.weapon_slots and ApocalypseSurvival.ClientData.weapon_slots[i] then
                weaponData = ApocalypseSurvival.ClientData.weapon_slots[i]
            end

            local isHovered = self:IsHovered()
            local borderColor = isHovered and SLOT_BORDER_HOVER or SLOT_BORDER_COLOR
            local bgColor = isHovered and SLOT_HOVER_COLOR or SLOT_COLOR

            -- Fond du slot
            surface.SetDrawColor(Color(bgColor.r - 10, bgColor.g - 10, bgColor.b - 10, bgColor.a))
            surface.DrawRect(2, 2, w-2, h-2)
            surface.SetDrawColor(bgColor)
            surface.DrawRect(0, 0, w-2, h-2)

            -- Bordure
            surface.SetDrawColor(borderColor)
            surface.DrawOutlinedRect(0, 0, w, h, 2)

            -- Grille si vide
            if not weaponData then
                surface.SetDrawColor(borderColor.r, borderColor.g, borderColor.b, 50)
                for x = 10, w-10, 10 do
                    surface.DrawLine(x, 5, x, h-5)
                end
                for y = 10, h-10, 10 do
                    surface.DrawLine(5, y, w-5, y)
                end

                -- Texte indicatif
                draw.SimpleText("SLOT " .. i, "DermaDefault", w/2, h/2, Color(150, 150, 150, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
            else
                -- Afficher l'arme équipée
                local weaponName = "Arme"
                local item = ApocalypseSurvival.GetItem(weaponData.itemId)
                if item then
                    weaponName = item.name
                end

                -- Fond coloré pour arme équipée
                surface.SetDrawColor(Color(150, 150, 100, 150))
                surface.DrawRect(3, 3, w-6, h-6)

                -- Nom de l'arme
                local shortName = string.sub(weaponName, 1, 6)
                if string.len(weaponName) > 6 then
                    shortName = shortName .. ".."
                end

                draw.SimpleText(shortName, "DermaDefault", w/2, 8, TEXT_HIGHLIGHT, TEXT_ALIGN_CENTER, TEXT_ALIGN_TOP)

                -- Indicateur d'arme équipée
                surface.SetDrawColor(Color(0, 255, 0, 200))
                surface.DrawRect(3, 3, 8, 8)
                draw.SimpleText("E", "DermaDefault", 7, 7, Color(0, 0, 0, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)

                -- Munitions dans le chargeur (obtenir depuis l'arme du joueur)
                local ply = LocalPlayer()
                if IsValid(ply) then
                    local weapon = ply:GetWeapon(weaponData.weaponClass)
                    if IsValid(weapon) then
                        local ammo = weapon:Clip1()
                        local maxAmmo = weapon:GetMaxClip1()
                        if maxAmmo > 0 then
                            draw.SimpleText(ammo .. "/" .. maxAmmo, "DermaDefault", w/2, h - 10, Color(255, 255, 0, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_BOTTOM)
                        end
                    end
                end
            end
        end

        -- Tooltip
        weaponSlot:SetTooltip("Slot d'arme " .. i .. "\nGlissez une arme ici pour l'équiper")

        -- Clic pour déséquiper
        weaponSlot.OnMousePressed = function(self, keyCode)
            if keyCode == MOUSE_RIGHT then
                -- Vérifier s'il y a une arme dans ce slot
                local weaponData = nil
                if ApocalypseSurvival.ClientData and ApocalypseSurvival.ClientData.weapon_slots and ApocalypseSurvival.ClientData.weapon_slots[i] then
                    weaponData = ApocalypseSurvival.ClientData.weapon_slots[i]
                end

                if weaponData then
                    -- Envoyer demande de déséquipement au serveur
                    net.Start("ApocalypseSurvival_UnequipWeapon")
                    net.WriteInt(i, 8)
                    net.SendToServer()
                end
            end
        end
    end

    -- Panel d'instructions style DayZ (ajusté pour les slots d'armes)
    local instructionsPanel = vgui.Create("DPanel", content)
    instructionsPanel:SetPos(10, panelH - 60)
    instructionsPanel:SetSize(panelW - 40, 50)
    instructionsPanel.Paint = function(self, w, h)
        surface.SetDrawColor(CATEGORY_HEADER)
        surface.DrawRect(0, 0, w, h)
        surface.SetDrawColor(ACCENT_COLOR)
        surface.DrawOutlinedRect(0, 0, w, h)

        -- Titre
        draw.SimpleText("CONTRÔLES", "DermaDefaultBold", 10, 5, TEXT_HIGHLIGHT, TEXT_ALIGN_LEFT, TEXT_ALIGN_TOP)

        -- Instructions
        local instructions = {
            "• Clic droit sur une arme pour l'équiper dans un slot libre",
            "• Clic droit sur un slot d'arme pour la déséquiper",
            "• [W] = Arme  [F] = Nourriture  [M] = Médical  [A] = Munitions",
            "• ⚠️ ARMES ET ACTIONS DÉSACTIVÉES PENDANT L'INVENTAIRE"
        }

        for i, instruction in ipairs(instructions) do
            draw.SimpleText(instruction, "DermaDefault", 15, 15 + (i * 15), TEXT_COLOR, TEXT_ALIGN_LEFT, TEXT_ALIGN_TOP)
        end
    end

    -- Bouton de fermeture alternatif en bas
    local closeBtn2 = vgui.Create("DButton", content)
    closeBtn2:SetPos(panelW - 120, panelH - 90)
    closeBtn2:SetSize(100, 30)
    closeBtn2:SetText("FERMER")
    closeBtn2:SetFont("DermaDefaultBold")
    closeBtn2:SetTextColor(Color(255, 255, 255, 255))
    closeBtn2:SetCursor("hand")

    closeBtn2.Paint = function(self, w, h)
        local isHovered = self:IsHovered()
        local color = isHovered and Color(200, 80, 80, 220) or Color(80, 80, 80, 200)

        surface.SetDrawColor(color)
        surface.DrawRect(0, 0, w, h)
        surface.SetDrawColor(ACCENT_COLOR)
        surface.DrawOutlinedRect(0, 0, w, h, 2)

        if isHovered then
            surface.SetDrawColor(Color(255, 255, 255, 30))
            surface.DrawRect(2, 2, w-4, h-4)
        end
    end

    closeBtn2.DoClick = function()
        print("[Apocalypse Survival] Bouton FERMER cliqué")
        ApocalypseSurvival.CloseInventory()
    end

    -- Test de cliquabilité
    closeBtn2.OnMousePressed = function(self, keyCode)
        print("[Apocalypse Survival] Mouse pressed sur bouton FERMER, keyCode:", keyCode)
        if keyCode == MOUSE_LEFT then
            ApocalypseSurvival.CloseInventory()
        end
    end
end

-- Ouvrir l'inventaire
function ApocalypseSurvival.OpenInventory()
    print("[Apocalypse Survival] Tentative d'ouverture de l'inventaire")

    if ApocalypseSurvival.InventoryOpen then
        print("[Apocalypse Survival] Inventaire déjà ouvert")
        return
    end

    print("[Apocalypse Survival] Ouverture de l'inventaire...")
    ApocalypseSurvival.InventoryOpen = true
    ApocalypseSurvival.CreateInventoryPanel()
    gui.EnableScreenClicker(true)

    -- Désactiver les armes et actions
    ApocalypseSurvival.DisableWeapons()

    print("[Apocalypse Survival] Inventaire ouvert avec succès")
end

-- Fermer l'inventaire
function ApocalypseSurvival.CloseInventory()
    print("[Apocalypse Survival] Tentative de fermeture de l'inventaire")

    if not ApocalypseSurvival.InventoryOpen then
        print("[Apocalypse Survival] Inventaire déjà fermé")
        return
    end

    print("[Apocalypse Survival] Fermeture de l'inventaire...")
    ApocalypseSurvival.InventoryOpen = false

    if IsValid(ApocalypseSurvival.InventoryPanel) then
        ApocalypseSurvival.InventoryPanel:Remove()
        ApocalypseSurvival.InventoryPanel = nil
    end

    gui.EnableScreenClicker(false)

    -- Réactiver les armes et actions
    ApocalypseSurvival.EnableWeapons()

    print("[Apocalypse Survival] Inventaire fermé avec succès")
end

-- Toggle inventaire
function ApocalypseSurvival.ToggleInventory()
    print("[Apocalypse Survival] Toggle inventaire appelé")
    if ApocalypseSurvival.InventoryOpen then
        print("[Apocalypse Survival] Fermeture de l'inventaire")
        ApocalypseSurvival.CloseInventory()
    else
        print("[Apocalypse Survival] Ouverture de l'inventaire")
        ApocalypseSurvival.OpenInventory()
    end
end

-- Gestion simplifiée des touches avec debug
ApocalypseSurvival.KeyPressed = false
ApocalypseSurvival.EscapePressed = false

hook.Add("Think", "ApocalypseSurvival_InventoryKey", function()
    local ply = LocalPlayer()
    if not IsValid(ply) then return end

    -- Debug: vérifier constamment l'état de la touche I
    local iPressed = input.IsKeyDown(KEY_I)

    -- Vérifier si la touche I est pressée
    if iPressed then
        if not ApocalypseSurvival.KeyPressed then
            ApocalypseSurvival.KeyPressed = true
            print("[Apocalypse Survival] Touche I détectée - État inventaire:", ApocalypseSurvival.InventoryOpen)
            ApocalypseSurvival.ToggleInventory()
        end
    else
        ApocalypseSurvival.KeyPressed = false
    end
end)

-- Hook alternatif pour PlayerButtonDown (méthode principale maintenant)
hook.Add("PlayerButtonDown", "ApocalypseSurvival_ButtonDown", function(ply, button)
    if ply ~= LocalPlayer() then return end

    -- Debug moins verbeux
    if button == KEY_I then
        print("[Apocalypse Survival] Touche I via PlayerButtonDown - KEY_I =", KEY_I)
        ApocalypseSurvival.ToggleInventory()
        return true -- Empêcher d'autres traitements
    end
end)

-- Hook pour les binds système (si disponible)
hook.Add("PlayerBindPress", "ApocalypseSurvival_BindPress", function(ply, bind, pressed)
    if ply ~= LocalPlayer() or not pressed then return end

    -- Vérifier si c'est un bind vers notre commande
    if string.find(bind, "apocalypse_inventory") then
        print("[Apocalypse Survival] Bind détecté:", bind)
        ApocalypseSurvival.ToggleInventory()
        return true
    end
end)

-- Fermer avec Échap
hook.Add("PlayerBindPress", "ApocalypseSurvival_CloseInventory", function(ply, bind, pressed)
    if ply == LocalPlayer() and pressed and ApocalypseSurvival.InventoryOpen then
        if bind == "cancelselect" or bind == "+menu" or bind == "+menu_context" then
            ApocalypseSurvival.CloseInventory()
            return true
        end
    end
end)

-- Gestion globale des touches de fermeture
hook.Add("Think", "ApocalypseSurvival_CloseKeys", function()
    if not ApocalypseSurvival.InventoryOpen then return end

    -- Échap pour fermer
    if input.IsKeyDown(KEY_ESCAPE) then
        if not ApocalypseSurvival.EscapePressed then
            ApocalypseSurvival.EscapePressed = true
            print("[Apocalypse Survival] Touche Échap détectée")
            ApocalypseSurvival.CloseInventory()
        end
    else
        ApocalypseSurvival.EscapePressed = false
    end
end)

-- Mettre à jour l'inventaire quand les données changent
local oldUpdateData = net.Receive
hook.Add("Think", "ApocalypseSurvival_UpdateInventory", function()
    if ApocalypseSurvival.InventoryOpen and IsValid(ApocalypseSurvival.InventoryPanel) then
        -- Recréer le panel si les données ont changé
        -- (Pour une version plus optimisée, on pourrait juste mettre à jour les slots modifiés)
        timer.Simple(0.1, function()
            if ApocalypseSurvival.InventoryOpen then
                ApocalypseSurvival.CreateInventoryPanel()
            end
        end)
    end
end)

-- Commandes console
concommand.Add("apocalypse_inventory", function()
    ApocalypseSurvival.ToggleInventory()
end)

concommand.Add("apocalypse_inventory_open", function()
    ApocalypseSurvival.OpenInventory()
end)

concommand.Add("apocalypse_inventory_close", function()
    ApocalypseSurvival.CloseInventory()
end)

-- Commande alternative
concommand.Add("+apocalypse_inventory", function()
    ApocalypseSurvival.OpenInventory()
end)

concommand.Add("-apocalypse_inventory", function()
    -- Ne rien faire sur le relâchement
end)

-- Initialisation sans bind (les binds sont bloqués)
hook.Add("InitPostEntity", "ApocalypseSurvival_Init", function()
    timer.Simple(1, function()
        if IsValid(LocalPlayer()) then
            print("[Apocalypse Survival] Initialisation sans bind (bind bloqué par le serveur)")
            print("[Apocalypse Survival] Utilisez la commande 'apocalypse_inventory' ou les hooks de touches")

            -- Test immédiat
            timer.Simple(0.5, function()
                print("[Apocalypse Survival] Test: KEY_I =", KEY_I)
                print("[Apocalypse Survival] Test: input.IsKeyDown disponible =", input.IsKeyDown ~= nil)
            end)
        end
    end)
end)

-- Hook de test pour vérifier si les touches fonctionnent (désactivé par défaut)
--[[
hook.Add("Think", "ApocalypseSurvival_KeyTest", function()
    -- Test périodique (toutes les 5 secondes)
    if math.floor(CurTime()) % 5 == 0 and CurTime() - math.floor(CurTime()) < 0.1 then
        if input.IsKeyDown(KEY_I) then
            print("[Apocalypse Survival] Test périodique: Touche I est pressée!")
        end
    end
end)
--]]

-- Commande de test pour vérifier l'état de l'inventaire
concommand.Add("apocalypse_debug", function()
    print("=== DEBUG APOCALYPSE SURVIVAL ===")
    print("InventoryOpen:", ApocalypseSurvival.InventoryOpen)
    print("InventoryPanel valid:", IsValid(ApocalypseSurvival.InventoryPanel))
    print("Screen clicker enabled:", gui.IsGameUIVisible())
    print("LocalPlayer valid:", IsValid(LocalPlayer()))
    print("KEY_I value:", KEY_I)
    print("input.IsKeyDown(KEY_I):", input.IsKeyDown(KEY_I))
    print("ApocalypseSurvival.KeyPressed:", ApocalypseSurvival.KeyPressed)

    if IsValid(ApocalypseSurvival.InventoryPanel) then
        local frame = ApocalypseSurvival.InventoryPanel
        print("Frame size:", frame:GetWide(), "x", frame:GetTall())
        print("Frame pos:", frame:GetPos())
        print("Frame visible:", frame:IsVisible())
    end
    print("================================")
end)

-- Commande pour tester la détection des touches en temps réel
concommand.Add("apocalypse_test_keys", function()
    print("[Apocalypse Survival] Test des touches activé - Appuyez sur I")

    local testHook = "ApocalypseSurvival_TestKeys"
    hook.Remove("Think", testHook)

    local startTime = CurTime()
    hook.Add("Think", testHook, function()
        if CurTime() - startTime > 10 then -- Test pendant 10 secondes
            hook.Remove("Think", testHook)
            print("[Apocalypse Survival] Test des touches terminé")
            return
        end

        if input.IsKeyDown(KEY_I) then
            print("[Apocalypse Survival] TOUCHE I DÉTECTÉE!")
            hook.Remove("Think", testHook)
        end
    end)
end)

-- Commande pour forcer l'ouverture (bypass des touches)
concommand.Add("apocalypse_force_open", function()
    print("[Apocalypse Survival] Ouverture forcée de l'inventaire")
    ApocalypseSurvival.OpenInventory()
end)

-- Méthode de fallback avec timer (si les hooks ne marchent pas)
concommand.Add("apocalypse_enable_timer", function()
    print("[Apocalypse Survival] Activation du timer de détection des touches")

    timer.Create("ApocalypseSurvival_KeyTimer", 0.1, 0, function()
        if not IsValid(LocalPlayer()) then return end

        if input.IsKeyDown(KEY_I) then
            if not ApocalypseSurvival.TimerKeyPressed then
                ApocalypseSurvival.TimerKeyPressed = true
                print("[Apocalypse Survival] Touche I détectée via timer")
                ApocalypseSurvival.ToggleInventory()
            end
        else
            ApocalypseSurvival.TimerKeyPressed = false
        end
    end)
end)

concommand.Add("apocalypse_disable_timer", function()
    timer.Remove("ApocalypseSurvival_KeyTimer")
    print("[Apocalypse Survival] Timer de détection désactivé")
end)

-- Instructions pour l'utilisateur
concommand.Add("apocalypse_help", function()
    print("=== APOCALYPSE SURVIVAL - AIDE ===")
    print("Commandes disponibles :")
    print("• apocalypse_inventory - Ouvrir/fermer l'inventaire")
    print("• apocalypse_force_open - Forcer l'ouverture")
    print("• apocalypse_debug - Informations de debug")
    print("• apocalypse_test_keys - Tester la détection des touches")
    print("• apocalypse_enable_timer - Activer détection par timer")
    print("• apocalypse_force_enable_weapons - Réactiver les armes")
    print("• apocalypse_help - Afficher cette aide")
    print("")
    print("Si la touche I ne fonctionne pas :")
    print("1. Essayez: apocalypse_enable_timer")
    print("2. Ou utilisez: apocalypse_inventory dans la console")
    print("")
    print("Si les armes restent bloquées :")
    print("• apocalypse_force_enable_weapons")
    print("===================================")
end)

-- Commande de sécurité pour réactiver les armes
concommand.Add("apocalypse_force_enable_weapons", function()
    print("[Apocalypse Survival] Réactivation forcée des armes")
    ApocalypseSurvival.WeaponsDisabled = false
    ApocalypseSurvival.InventoryOpen = false
    ApocalypseSurvival.EnableWeapons()

    if IsValid(ApocalypseSurvival.InventoryPanel) then
        ApocalypseSurvival.InventoryPanel:Remove()
        ApocalypseSurvival.InventoryPanel = nil
    end

    gui.EnableScreenClicker(false)
    print("[Apocalypse Survival] Armes réactivées et inventaire fermé")
end)

-- Hook de sécurité pour réactiver les armes si le joueur meurt ou respawn
hook.Add("PlayerSpawn", "ApocalypseSurvival_SafetyReset", function(ply)
    if ply == LocalPlayer() then
        timer.Simple(0.1, function()
            if ApocalypseSurvival.WeaponsDisabled then
                print("[Apocalypse Survival] Réactivation automatique des armes après spawn")
                ApocalypseSurvival.EnableWeapons()
                ApocalypseSurvival.InventoryOpen = false
                gui.EnableScreenClicker(false)
            end
        end)
    end
end)
